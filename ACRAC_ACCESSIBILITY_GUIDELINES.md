# ACRAC无障碍访问指南

## 1. 无障碍访问原则

### 1.1 感知性
信息和用户界面组件必须以用户能够感知的方式呈现。确保所有用户都能通过至少一种感官（视觉、听觉或触觉）来感知界面内容。

### 1.2 可操作性
用户界面组件和导航必须可以通过多种方式操作。确保所有功能都可以通过键盘、语音输入或其他辅助技术来操作。

### 1.3 可理解性
信息和用户界面的操作必须是可理解的。确保用户能够理解界面内容和操作方式。

### 1.4 健壮性
内容必须足够健壮，能够与当前和未来的用户代理（包括辅助技术）兼容。

## 2. WCAG 2.1 AA级标准实现

### 2.1 颜色对比度要求
确保文本和背景之间有足够的对比度，满足4.5:1的对比度比例（大号文本为3:1）。

```scss
// 颜色对比度检查
.text-primary {
  color: $gray-900; // #212529
  background: $white; // #FFFFFF
  // 对比度: 15.9:1 (满足要求)
}

.text-secondary {
  color: $gray-600; // #6C757D
  background: $white; // #FFFFFF
  // 对比度: 4.5:1 (满足要求)
}

.text-disabled {
  color: $gray-400; // #CED4DA
  background: $white; // #FFFFFF
  // 对比度: 2.8:1 (不满足要求，需要调整)
}
```

### 2.2 文本大小和缩放
确保文本可以放大到200%而不会丢失内容或功能。

```scss
// 支持文本缩放
html {
  font-size: 16px; // 基准字体大小
  
  @media (max-width: $breakpoint-md) {
    font-size: 14px; // 移动端适当减小
  }
}

// 使用相对单位
.card {
  font-size: 1rem; // 相对于根元素
  line-height: 1.5;
}

.title {
  font-size: 1.5rem; // 相对于根元素
}
```

### 2.3 键盘导航
确保所有功能都可以通过键盘访问，不依赖鼠标操作。

```vue
<template>
  <div 
    class="interactive-element"
    tabindex="0"
    @keydown.enter="handleEnter"
    @keydown.space="handleSpace"
    @keydown.esc="handleEscape"
    @focus="handleFocus"
    @blur="handleBlur"
  >
    <!-- 交互元素内容 -->
  </div>
</template>

<script>
export default {
  methods: {
    handleEnter(event) {
      // 处理Enter键
      this.performAction();
    },
    
    handleSpace(event) {
      // 处理空格键
      event.preventDefault(); // 防止页面滚动
      this.performAction();
    },
    
    handleEscape(event) {
      // 处理Esc键
      this.close();
    },
    
    handleFocus() {
      // 处理焦点获得
      this.isFocused = true;
    },
    
    handleBlur() {
      // 处理焦点失去
      this.isFocused = false;
    }
  }
}
</script>
```

## 3. 语义化HTML结构

### 3.1 正确使用HTML标签
使用语义化的HTML标签来表达内容结构。

```vue
<template>
  <main>
    <header>
      <h1>页面标题</h1>
      <nav>
        <ul>
          <li><a href="#section1">章节1</a></li>
          <li><a href="#section2">章节2</a></li>
        </ul>
      </nav>
    </header>
    
    <section id="section1">
      <h2>章节1标题</h2>
      <p>章节1内容</p>
    </section>
    
    <section id="section2">
      <h2>章节2标题</h2>
      <form>
        <label for="name">姓名</label>
        <input type="text" id="name" name="name" required>
        
        <label for="email">邮箱</label>
        <input type="email" id="email" name="email" required>
        
        <button type="submit">提交</button>
      </form>
    </section>
    
    <aside>
      <h3>相关内容</h3>
      <ul>
        <li><a href="#">相关链接1</a></li>
        <li><a href="#">相关链接2</a></li>
      </ul>
    </aside>
    
    <footer>
      <p>&copy; 2023 ACRAC系统</p>
    </footer>
  </main>
</template>
```

### 3.2 标题层次结构
确保标题按层次结构正确使用，不跳过级别。

```vue
<template>
  <article>
    <h1>文章标题</h1>
    
    <section>
      <h2>主要章节</h2>
      
      <section>
        <h3>子章节</h3>
        
        <section>
          <h4>更小的章节</h4>
          <p>内容...</p>
        </section>
      </section>
    </section>
    
    <section>
      <h2>另一个主要章节</h2>
      <p>内容...</p>
    </section>
  </article>
</template>
```

## 4. ARIA属性应用

### 4.1 角色和属性
为复杂组件添加适当的ARIA角色和属性。

```vue
<template>
  <div 
    role="alert"
    aria-live="polite"
    :aria-hidden="!showMessage"
    class="alert"
    :class="{ 'alert--visible': showMessage }"
  >
    {{ message }}
  </div>
  
  <button 
    :aria-label="isExpanded ? '收起详情' : '展开详情'"
    :aria-expanded="isExpanded"
    @click="toggleExpand"
    class="expand-button"
  >
    <svg :aria-hidden="true" class="icon">
      <use :xlink:href="isExpanded ? '#icon-collapse' : '#icon-expand'"></use>
    </svg>
  </button>
  
  <div 
    id="details-panel"
    role="region"
    aria-labelledby="details-heading"
    :aria-hidden="!isExpanded"
  >
    <h3 id="details-heading">详细信息</h3>
    <p>详细内容...</p>
  </div>
</template>
```

### 4.2 表单无障碍
为表单元素提供适当的标签和描述。

```vue
<template>
  <form @submit.prevent="handleSubmit">
    <div class="form-group">
      <label for="search-input">搜索关键词 *</label>
      <input 
        id="search-input"
        v-model="searchQuery"
        type="text"
        required
        aria-describedby="search-help search-error"
        :aria-invalid="hasError"
      />
      <p id="search-help" class="form-help">
        请输入您要搜索的关键词，支持中英文
      </p>
      <p 
        v-if="hasError" 
        id="search-error" 
        class="form-error" 
        role="alert"
        aria-live="polite"
      >
        {{ errorMessage }}
      </p>
    </div>
    
    <button 
      type="submit"
      :disabled="isSubmitting"
      aria-busy="false"
      :aria-disabled="isSubmitting"
    >
      <span v-if="isSubmitting">搜索中...</span>
      <span v-else>搜索</span>
    </button>
  </form>
</template>
```

## 5. 焦点管理

### 5.1 焦点指示器
为焦点元素提供清晰可见的指示器。

```scss
// 焦点指示器样式
:focus {
  outline: 2px solid $primary-color;
  outline-offset: 2px;
}

// 自定义组件焦点样式
.custom-button {
  position: relative;
  
  &:focus {
    outline: none;
    
    &::after {
      content: '';
      position: absolute;
      top: -4px;
      left: -4px;
      right: -4px;
      bottom: -4px;
      border: 2px solid $primary-color;
      border-radius: $border-radius-sm;
    }
  }
}

// 减少动画偏好支持
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
```

### 5.2 焦点陷阱
在模态框等场景中实现焦点陷阱。

```vue
<template>
  <div 
    v-if="isVisible"
    ref="modal"
    role="dialog"
    aria-modal="true"
    :aria-labelledby="titleId"
    :aria-describedby="contentId"
    class="modal"
    @keydown.esc="close"
  >
    <div class="modal-content">
      <header>
        <h2 :id="titleId">模态框标题</h2>
        <button 
          @click="close"
          aria-label="关闭"
          ref="closeButton"
        >
          ×
        </button>
      </header>
      
      <div :id="contentId" class="modal-body">
        <!-- 模态框内容 -->
      </div>
      
      <footer>
        <button @click="confirm">确认</button>
        <button @click="cancel" ref="cancelButton">取消</button>
      </footer>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      titleId: 'modal-title-' + Date.now(),
      contentId: 'modal-content-' + Date.now(),
      focusableElements: []
    }
  },
  
  mounted() {
    this.setupFocusTrap();
    this.$refs.closeButton.focus();
  },
  
  methods: {
    setupFocusTrap() {
      const focusableSelectors = [
        'a[href]',
        'button:not([disabled])',
        'input:not([disabled])',
        'select:not([disabled])',
        'textarea:not([disabled])',
        '[tabindex]:not([tabindex="-1"])'
      ];
      
      this.focusableElements = Array.from(
        this.$refs.modal.querySelectorAll(focusableSelectors.join(','))
      ).filter(el => {
        return el.offsetWidth > 0 || el.offsetHeight > 0;
      });
    },
    
    handleTabKey(event) {
      if (this.focusableElements.length === 0) return;
      
      const firstElement = this.focusableElements[0];
      const lastElement = this.focusableElements[this.focusableElements.length - 1];
      
      if (event.shiftKey && document.activeElement === firstElement) {
        event.preventDefault();
        lastElement.focus();
      } else if (!event.shiftKey && document.activeElement === lastElement) {
        event.preventDefault();
        firstElement.focus();
      }
    }
  }
}
</script>
```

## 6. 屏幕阅读器支持

### 6.1 动态内容通知
使用aria-live属性通知屏幕阅读器动态内容变化。

```vue
<template>
  <div class="notification-area">
    <div 
      v-if="notification"
      role="alert"
      aria-live="assertive"
      class="notification"
    >
      {{ notification.message }}
    </div>
    
    <div 
      v-if="statusMessage"
      aria-live="polite"
      class="status-message"
    >
      {{ statusMessage }}
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      notification: null,
      statusMessage: ''
    }
  },
  
  methods: {
    showNotification(message, type = 'info') {
      this.notification = {
        message,
        type,
        timestamp: Date.now()
      };
      
      // 5秒后自动清除
      setTimeout(() => {
        this.notification = null;
      }, 5000);
    },
    
    updateStatus(message) {
      this.statusMessage = message;
    }
  }
}
</script>
```

### 6.2 图标和图像
为图标和图像提供适当的替代文本。

```vue
<template>
  <div class="icon-button">
    <svg 
      aria-hidden="true"
      class="icon"
      width="24"
      height="24"
      role="img"
    >
      <use :xlink:href="`#icon-${iconName}`"></use>
    </svg>
    <span class="sr-only">{{ iconLabel }}</span>
  </div>
  
  <img 
    :src="imageSrc"
    :alt="imageAlt"
    :title="imageTitle"
    class="content-image"
  />
</template>

<script>
export default {
  props: {
    iconName: String,
    iconLabel: String,
    imageSrc: String,
    imageAlt: String,
    imageTitle: String
  }
}
</script>

<style>
/* 屏幕阅读器专用文本 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
</style>
```

## 7. 键盘快捷键

### 7.1 常用快捷键
为常用操作提供键盘快捷键支持。

```vue
<template>
  <div 
    class="application"
    @keydown="handleGlobalKeydown"
  >
    <!-- 应用内容 -->
  </div>
</template>

<script>
export default {
  methods: {
    handleGlobalKeydown(event) {
      // 快捷键处理
      if (event.ctrlKey || event.metaKey) {
        switch (event.key) {
          case 'k':
            event.preventDefault();
            this.openSearch();
            break;
          case 'n':
            event.preventDefault();
            this.createNew();
            break;
          case 's':
            event.preventDefault();
            this.save();
            break;
        }
      }
      
      // 导航快捷键
      switch (event.key) {
        case '?':
          this.showHelp();
          break;
        case 'Escape':
          this.closeModal();
          break;
      }
    },
    
    openSearch() {
      // 打开搜索功能
    },
    
    createNew() {
      // 创建新项目
    },
    
    save() {
      // 保存操作
    },
    
    showHelp() {
      // 显示帮助信息
    },
    
    closeModal() {
      // 关闭模态框
    }
  }
}
</script>
```

## 8. 高对比度模式

### 8.1 CSS媒体查询
使用CSS媒体查询支持高对比度模式。

```scss
// 高对比度模式支持
@media (prefers-contrast: high) {
  .card {
    border: 2px solid $gray-900;
    background: $white;
  }
  
  .button {
    border: 2px solid currentColor;
    background: $white;
    color: $gray-900;
  }
  
  .link {
    text-decoration: underline;
  }
  
  .form-control {
    border: 2px solid $gray-900;
  }
}

// 减少透明度偏好
@media (prefers-reduced-transparency: reduce) {
  .modal-overlay {
    background: rgba(0, 0, 0, 0.8); // 减少透明度
  }
  
  .card {
    background: $white;
  }
}
```

## 9. 文本缩放支持

### 9.1 相对单位使用
使用相对单位确保文本可以正常缩放。

```scss
// 使用相对单位
html {
  font-size: 16px; // 基准大小
}

body {
  font-size: 1rem; // 相对于html
  line-height: 1.5;
}

h1 {
  font-size: 2rem; // 相对于html
}

h2 {
  font-size: 1.5rem;
}

// 容器使用相对单位
.container {
  max-width: 75rem; // 1200px
  padding: 1rem;
}

// 间距使用相对单位
.spacing {
  margin: 1rem 0;
  padding: 0.5rem;
}
```

## 10. 测试和验证

### 10.1 自动化测试
使用自动化工具进行无障碍测试。

```javascript
// Jest测试示例
describe('无障碍访问测试', () => {
  test('按钮应有适当的ARIA标签', () => {
    const wrapper = mount(Button, {
      props: {
        ariaLabel: '搜索'
      }
    });
    
    expect(wrapper.attributes('aria-label')).toBe('搜索');
  });
  
  test('表单应有关联的标签', () => {
    const wrapper = mount(FormInput, {
      props: {
        label: '姓名',
        id: 'name'
      }
    });
    
    const label = wrapper.find('label');
    const input = wrapper.find('input');
    
    expect(label.attributes('for')).toBe('name');
    expect(input.attributes('id')).toBe('name');
  });
});
```

### 10.2 手动测试
使用辅助技术进行手动测试。

```markdown
## 手动测试清单

### 键盘导航测试
- [ ] 所有交互元素可通过Tab键访问
- [ ] Tab键顺序符合逻辑
- [ ] Shift+Tab可反向导航
- [ ] Enter键可激活按钮和链接
- [ ] 空格键可激活复选框和按钮
- [ ] Esc键可关闭模态框
- [ ] 焦点指示器清晰可见

### 屏幕阅读器测试
- [ ] NVDA (Windows)
- [ ] JAWS (Windows)
- [ ] VoiceOver (macOS/iOS)
- [ ] TalkBack (Android)

### 高对比度测试
- [ ] Windows高对比度模式
- [ ] macOS增加对比度
- [ ] 移动端反转颜色

### 文本缩放测试
- [ ] 200%缩放无内容丢失
- [ ] 300%缩放功能正常
- [ ] 400%缩放布局合理
```

## 11. 持续改进

### 11.1 用户反馈
建立用户反馈机制收集无障碍使用体验。

```vue
<template>
  <div class="accessibility-feedback">
    <h3>无障碍访问反馈</h3>
    <p>如果您在使用过程中遇到任何无障碍访问问题，请告诉我们：</p>
    
    <form @submit.prevent="submitFeedback">
      <div class="form-group">
        <label for="feedback-type">问题类型</label>
        <select id="feedback-type" v-model="feedbackType">
          <option value="keyboard">键盘导航问题</option>
          <option value="screenreader">屏幕阅读器问题</option>
          <option value="contrast">颜色对比度问题</option>
          <option value="other">其他问题</option>
        </select>
      </div>
      
      <div class="form-group">
        <label for="feedback-description">问题描述</label>
        <textarea 
          id="feedback-description"
          v-model="feedbackDescription"
          rows="4"
          required
        ></textarea>
      </div>
      
      <button type="submit">提交反馈</button>
    </form>
  </div>
</template>
```

### 11.2 定期审计
定期进行无障碍访问审计。

```markdown
## 无障碍访问审计计划

### 月度审计
- 自动化测试运行
- 新功能无障碍检查
- 用户反馈处理

### 季度审计
- 完整无障碍测试
- 辅助技术兼容性测试
- 第三方组件审计

### 年度审计
- 全面WCAG合规性检查
- 用户体验评估
- 技术债务清理
```

通过遵循这些无障碍访问指南，ACRAC系统将能够为所有用户提供更好的使用体验，包括那些使用辅助技术的用户。