# ACRAC设计系统

## 1. 设计理念

### 1.1 医学专业性
设计系统以医疗行业专业性为核心，体现医学严谨、精准、可靠的特点，为医生和医疗工作者提供专业、可信的界面体验。

### 1.2 用户中心设计
以医生和医疗工作者的实际工作流程为中心，优化信息架构和交互设计，提升工作效率和使用体验。

### 1.3 数据可视化驱动
强化数据展示效果，通过直观的图表和可视化手段，帮助用户快速理解和分析医疗数据。

## 2. 颜色系统

### 2.1 主色调
医疗蓝作为主色调，象征专业、信任和科技感。

```scss
// 主色调 - 医疗蓝
$primary-color: #2E86AB;        // 主色
$primary-light: #5BA3C7;        // 浅色
$primary-dark: #1A5F80;         // 深色
$primary-hover: #3A9BC9;        // 悬停色
$primary-active: #256D8A;       // 激活色
$primary-disabled: #A9CDD9;     // 禁用色
```

### 2.2 功能色
```scss
// 功能色彩
$success-color: #28A745;        // 成功
$success-light: #D4EDDA;        // 成功浅色
$warning-color: #FFC107;        // 警告
$warning-light: #FFF3CD;        // 警告浅色
$error-color: #DC3545;          // 错误
$error-light: #F8D7DA;          // 错误浅色
$info-color: #17A2B8;           // 信息
$info-light: #D1ECF1;           // 信息浅色
```

### 2.3 中性色
```scss
// 中性色
$white: #FFFFFF;                // 白色
$black: #000000;                // 黑色

// 灰色系
$gray-50: #F8F9FA;             // 背景色
$gray-100: #F1F3F5;            // 次背景色
$gray-200: #E9ECEF;            // 边框色
$gray-300: #DEE2E6;            // 次边框色
$gray-400: #CED4DA;            // 占位符
$gray-500: #ADB5BD;            // 次要文字
$gray-600: #6C757D;            // 文字
$gray-700: #495057;            // 主要文字
$gray-800: #343A40;            // 标题文字
$gray-900: #212529;            // 重要文字
```

### 2.4 医疗专业色
```scss
// 医疗专业色
$appropriateness-high: #28A745;     // 通常适宜
$appropriateness-medium: #FFC107;   // 可能适宜
$appropriateness-low: #DC3545;      // 通常不适宜

$radiation-none: #28A745;           // 无辐射
$radiation-low: #FFC107;            // 低辐射
$radiation-medium: #FD7E14;         // 中辐射
$radiation-high: #DC3545;           // 高辐射

$evidence-level-A: #28A745;         // A级证据
$evidence-level-B: #17A2B8;         // B级证据
$evidence-level-C: #FFC107;         // C级证据
$evidence-level-D: #DC3545;         // D级证据
```

### 2.5 颜色使用规范
1. 主色调用于重要操作和关键信息
2. 功能色用于状态指示和反馈
3. 中性色用于背景、文字和边框
4. 医疗专业色用于特定医疗数据展示

## 3. 字体系统

### 3.1 字体族
```scss
// 字体系统
$font-family-zh: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
$font-family-en: 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
$font-family-mono: 'SFMono-Regular', 'Menlo', 'Monaco', 'Consolas', monospace;
```

### 3.2 字号规范
```scss
// 字号系统
$font-size-xs: 12px;        // 辅助文字
$font-size-sm: 14px;        // 正文小号
$font-size-base: 16px;      // 正文
$font-size-lg: 18px;        // 小标题
$font-size-xl: 20px;        // 标题
$font-size-2xl: 24px;       // 大标题
$font-size-3xl: 30px;       // 主标题
$font-size-4xl: 36px;       // 页眉标题
```

### 3.3 字重规范
```scss
// 字重
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;
```

### 3.4 行高规范
```scss
// 行高
$line-height-xs: 1.2;
$line-height-sm: 1.3;
$line-height-base: 1.5;
$line-height-lg: 1.6;
$line-height-xl: 1.7;
```

## 4. 间距系统

### 4.1 间距规范
基于8px栅格系统，确保界面元素间距统一协调。

```scss
// 间距系统 (8px栅格)
$spacing-xxs: 2px;      // 极小间距
$spacing-xs: 4px;       // 微小间距
$spacing-sm: 8px;       // 小间距
$spacing-md: 16px;      // 中等间距
$spacing-lg: 24px;      // 大间距
$spacing-xl: 32px;      // 超大间距
$spacing-2xl: 48px;     // 特大间距
$spacing-3xl: 64px;     // 极大间距
```

### 4.2 使用场景
1. **xxs (2px)**：元素内极小间距
2. **xs (4px)**：图标与文字间距
3. **sm (8px)**：同类元素间距
4. **md (16px)**：模块内元素间距
5. **lg (24px)**：模块间间距
6. **xl (32px)**：大模块间间距
7. **2xl (48px)**：页面区域间距
8. **3xl (64px)**：页面间大间距

## 5. 圆角系统

### 5.1 圆角规范
```scss
// 圆角系统
$border-radius-xs: 2px;     // 极小圆角
$border-radius-sm: 4px;     // 小圆角
$border-radius-md: 8px;     // 中等圆角
$border-radius-lg: 12px;    // 大圆角
$border-radius-xl: 16px;    // 超大圆角
$border-radius-full: 50%;   // 圆形
```

### 5.2 使用场景
1. **xs (2px)**：微小元素圆角
2. **sm (4px)**：输入框、按钮圆角
3. **md (8px)**：卡片、面板圆角
4. **lg (12px)**：大卡片圆角
5. **xl (16px)**：页面容器圆角
6. **full (50%)**：圆形图标、头像

## 6. 阴影系统

### 6.1 阴影规范
```scss
// 阴影系统
$shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);                    // 极小阴影
$shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);  // 小阴影
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); // 中等阴影
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); // 大阴影
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); // 超大阴影
$shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);          // 内阴影
```

### 6.2 使用场景
1. **xs**：细微层次区分
2. **sm**：卡片、按钮默认阴影
3. **md**：悬停状态阴影
4. **lg**：弹窗、下拉菜单
5. **xl**：重要模块、模态框
6. **inner**：凹陷效果

## 7. 响应式系统

### 7.1 断点规范
```scss
// 响应式断点
$breakpoint-xs: 0;          // 超小屏 (0-575px)
$breakpoint-sm: 576px;      // 小屏 (576px-767px)
$breakpoint-md: 768px;      // 中屏 (768px-991px)
$breakpoint-lg: 992px;      // 大屏 (992px-1199px)
$breakpoint-xl: 1200px;     // 超大屏 (1200px-1599px)
$breakpoint-xxl: 1600px;    // 特大屏 (1600px+)
```

### 7.2 媒体查询混入
```scss
// 媒体查询混入
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: $breakpoint-sm - 1) { @content; }
  } @else if $breakpoint == sm {
    @media (min-width: $breakpoint-sm) and (max-width: $breakpoint-md - 1) { @content; }
  } @else if $breakpoint == md {
    @media (min-width: $breakpoint-md) and (max-width: $breakpoint-lg - 1) { @content; }
  } @else if $breakpoint == lg {
    @media (min-width: $breakpoint-lg) and (max-width: $breakpoint-xl - 1) { @content; }
  } @else if $breakpoint == xl {
    @media (min-width: $breakpoint-xl) and (max-width: $breakpoint-xxl - 1) { @content; }
  } @else if $breakpoint == xxl {
    @media (min-width: $breakpoint-xxl) { @content; }
  }
}

// 移动优先混入
@mixin respond-up($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: $breakpoint-sm) { @content; }
  } @else if $breakpoint == md {
    @media (min-width: $breakpoint-md) { @content; }
  } @else if $breakpoint == lg {
    @media (min-width: $breakpoint-lg) { @content; }
  } @else if $breakpoint == xl {
    @media (min-width: $breakpoint-xl) { @content; }
  } @else if $breakpoint == xxl {
    @media (min-width: $breakpoint-xxl) { @content; }
  }
}
```

## 8. 图标系统

### 8.1 图标规范
1. 使用线性图标保持界面简洁
2. 图标大小与文字大小协调
3. 图标颜色与文字颜色一致
4. 提供图标悬停效果

### 8.2 图标尺寸
```scss
// 图标尺寸
$icon-size-xs: 12px;    // 极小图标
$icon-size-sm: 16px;    // 小图标
$icon-size-md: 20px;    // 中等图标
$icon-size-lg: 24px;    // 大图标
$icon-size-xl: 32px;    // 超大图标
```

## 9. 动效系统

### 9.1 过渡时间
```scss
// 过渡时间
$transition-duration-fast: 150ms;
$transition-duration-normal: 300ms;
$transition-duration-slow: 500ms;
```

### 9.2 缓动函数
```scss
// 缓动函数
$ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
$ease-out: cubic-bezier(0, 0, 0.2, 1);
$ease-in: cubic-bezier(0.4, 0, 1, 1);
```

### 9.3 常用动效
```scss
// 常用动效类
.fade-enter-active,
.fade-leave-active {
  transition: opacity $transition-duration-normal $ease-in-out;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-enter-active,
.slide-leave-active {
  transition: transform $transition-duration-normal $ease-in-out;
}

.slide-enter-from {
  transform: translateX(100%);
}

.slide-leave-to {
  transform: translateX(-100%);
}
```

## 10. 组件设计规范

### 10.1 按钮组件
```scss
// 按钮规范
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: $spacing-sm $spacing-md;
  border: 1px solid transparent;
  border-radius: $border-radius-sm;
  font-size: $font-size-base;
  font-weight: $font-weight-medium;
  cursor: pointer;
  transition: all $transition-duration-fast $ease-in-out;
  user-select: none;
  
  // 主要按钮
  &--primary {
    background-color: $primary-color;
    color: $white;
    border-color: $primary-color;
    
    &:hover {
      background-color: $primary-hover;
      border-color: $primary-hover;
    }
    
    &:active {
      background-color: $primary-active;
      border-color: $primary-active;
    }
    
    &:disabled {
      background-color: $primary-disabled;
      border-color: $primary-disabled;
      cursor: not-allowed;
    }
  }
  
  // 次要按钮
  &--secondary {
    background-color: $white;
    color: $primary-color;
    border-color: $primary-color;
    
    &:hover {
      background-color: $primary-light;
      border-color: $primary-color;
    }
  }
  
  // 尺寸变体
  &--small {
    padding: $spacing-xs $spacing-sm;
    font-size: $font-size-sm;
  }
  
  &--large {
    padding: $spacing-md $spacing-lg;
    font-size: $font-size-lg;
  }
}
```

### 10.2 卡片组件
```scss
// 卡片规范
.card {
  background: $white;
  border-radius: $border-radius-md;
  box-shadow: $shadow-sm;
  overflow: hidden;
  transition: box-shadow $transition-duration-normal;
  
  &:hover {
    box-shadow: $shadow-md;
  }
  
  &__header {
    padding: $spacing-md $spacing-lg;
    border-bottom: 1px solid $gray-200;
  }
  
  &__body {
    padding: $spacing-lg;
  }
  
  &__footer {
    padding: $spacing-md $spacing-lg;
    border-top: 1px solid $gray-200;
  }
}
```

### 10.3 表单组件
```scss
// 表单规范
.form {
  &__group {
    margin-bottom: $spacing-lg;
  }
  
  &__label {
    display: block;
    margin-bottom: $spacing-xs;
    font-weight: $font-weight-medium;
    color: $gray-700;
  }
  
  &__control {
    width: 100%;
    padding: $spacing-sm $spacing-md;
    border: 1px solid $gray-300;
    border-radius: $border-radius-sm;
    font-size: $font-size-base;
    transition: border-color $transition-duration-fast, box-shadow $transition-duration-fast;
    
    &:focus {
      border-color: $primary-color;
      outline: 0;
      box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
    }
    
    &:disabled {
      background-color: $gray-100;
      cursor: not-allowed;
    }
  }
  
  &__help {
    margin-top: $spacing-xs;
    font-size: $font-size-sm;
    color: $gray-500;
  }
  
  &__error {
    margin-top: $spacing-xs;
    font-size: $font-size-sm;
    color: $error-color;
  }
}
```

## 11. 可访问性规范

### 11.1 颜色对比度
1. 文字与背景对比度不低于4.5:1
2. 大号文字对比度不低于3:1
3. 交互元素状态变化应保持足够对比度

### 11.2 键盘导航
1. 所有交互元素可通过Tab键访问
2. 提供清晰的焦点指示器
3. 支持常用键盘快捷键

### 11.3 屏幕阅读器
1. 为图标提供替代文本
2. 使用语义化HTML标签
3. 为动态内容提供aria-live属性

## 12. 国际化规范

### 12.1 文本方向
1. 支持从左到右(LTR)和从右到左(RTL)布局
2. 为不同语言提供合适的字体族
3. 考虑不同语言的文本长度差异

### 12.2 日期时间格式
1. 根据地区设置显示日期时间格式
2. 提供时区支持
3. 考虑不同文化背景下的日期理解差异

## 13. 主题系统

### 13.1 明暗主题
```scss
// 明暗主题变量
:root {
  // 明色主题
  --bg-color: #{$white};
  --text-color: #{$gray-900};
  --border-color: #{$gray-300};
  --card-bg: #{$white};
}

@media (prefers-color-scheme: dark) {
  :root {
    // 暗色主题
    --bg-color: #1a1a1a;
    --text-color: #e0e0e0;
    --border-color: #404040;
    --card-bg: #2d2d2d;
  }
}
```

### 13.2 主题切换
1. 支持系统主题偏好
2. 提供手动主题切换功能
3. 保持主题切换时的状态一致性

## 14. 使用指南

### 14.1 引入方式
```scss
// 在项目入口文件中引入
@import 'design-system/variables';
@import 'design-system/mixins';
@import 'design-system/components';
```

### 14.2 使用示例
```scss
// 使用设计系统变量
.card {
  background-color: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: $border-radius-md;
  padding: $spacing-lg;
  
  &__title {
    font-size: $font-size-xl;
    font-weight: $font-weight-semibold;
    color: var(--text-color);
    margin-bottom: $spacing-md;
  }
  
  &__content {
    font-size: $font-size-base;
    line-height: $line-height-base;
    color: var(--text-color);
  }
}
```

### 14.3 自定义扩展
1. 基于现有变量进行扩展
2. 遵循命名规范
3. 保持与设计系统一致性