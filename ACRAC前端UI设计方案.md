# ACRAC医疗智能推荐系统前端UI设计方案

## 1. 系统架构概述

### 1.1 技术栈分析
基于backend分析，系统采用以下技术架构：
- **数据库层**：PostgreSQL + pgvector向量存储
- **后端服务**：FastAPI + SQLAlchemy + Pydantic
- **AI服务**：SiliconFlow API + Ollama Qwen服务
- **前端框架**：Vue 3 + TypeScript + Vite
- **UI组件库**：建议使用Element Plus或Ant Design Vue

### 1.2 核心数据模型
系统采用五表分离架构：
- **Panel（科室/专科）**：P0001, P0002...
- **Topic（临床主题）**：T0001, T0002...
- **ClinicalScenario（临床场景）**：S0001, S0002...
- **ProcedureDictionary（检查项目字典）**：PR0001, PR0002...
- **ClinicalRecommendation（临床推荐）**：CR000001, CR000002...

## 2. 页面布局架构

### 2.1 整体布局结构
```
┌─────────────────────────────────────────────────────────┐
│                    顶部导航栏                              │
├─────────────────────────────────────────────────────────┤
│ 侧边栏 │                主内容区域                        │
│ 导航   │                                               │
│       │  ┌─────────────────────────────────────────┐   │
│       │  │            页面标题区                    │   │
│       │  ├─────────────────────────────────────────┤   │
│       │  │            工具栏/操作区                  │   │
│       │  ├─────────────────────────────────────────┤   │
│       │  │                                        │   │
│       │  │            核心内容区                    │   │
│       │  │                                        │   │
│       │  │                                        │   │
│       │  └─────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────┘
```

### 2.2 响应式设计规范
- **桌面端（≥1200px）**：完整侧边栏 + 主内容区
- **平板端（768px-1199px）**：可折叠侧边栏 + 主内容区
- **移动端（<768px）**：底部导航 + 全屏主内容区

## 3. 核心功能模块划分

### 3.1 主导航模块
```typescript
interface NavigationItem {
  key: string
  title: string
  icon: string
  path: string
  children?: NavigationItem[]
  permission?: string[]
}

const navigationConfig: NavigationItem[] = [
  {
    key: 'dashboard',
    title: '工作台',
    icon: 'dashboard',
    path: '/dashboard'
  },
  {
    key: 'intelligent',
    title: '智能推荐',
    icon: 'brain',
    path: '/intelligent',
    children: [
      { key: 'three-methods', title: '三种推荐方法', path: '/intelligent/three-methods' },
      { key: 'clinical-analysis', title: '临床案例分析', path: '/intelligent/analysis' },
      { key: 'comparison', title: '方法对比', path: '/intelligent/comparison' }
    ]
  },
  {
    key: 'search',
    title: '向量检索',
    icon: 'search',
    path: '/search',
    children: [
      { key: 'comprehensive', title: '综合检索', path: '/search/comprehensive' },
      { key: 'panels', title: '科室检索', path: '/search/panels' },
      { key: 'scenarios', title: '场景检索', path: '/search/scenarios' },
      { key: 'procedures', title: '项目检索', path: '/search/procedures' }
    ]
  },
  {
    key: 'browse',
    title: '数据浏览',
    icon: 'folder',
    path: '/browse',
    children: [
      { key: 'hierarchy', title: '层级浏览', path: '/browse/hierarchy' },
      { key: 'panels', title: '科室管理', path: '/browse/panels' },
      { key: 'topics', title: '主题管理', path: '/browse/topics' },
      { key: 'scenarios', title: '场景管理', path: '/browse/scenarios' },
      { key: 'procedures', title: '项目管理', path: '/browse/procedures' }
    ]
  },
  {
    key: 'management',
    title: '系统管理',
    icon: 'setting',
    path: '/management',
    permission: ['admin', 'editor'],
    children: [
      { key: 'data-import', title: '数据导入', path: '/management/import' },
      { key: 'vector-build', title: '向量构建', path: '/management/vector' },
      { key: 'system-stats', title: '系统统计', path: '/management/stats' }
    ]
  }
]
```

### 3.2 智能推荐模块

#### 3.2.1 三种推荐方法页面
```vue
<template>
  <div class="recommendation-methods">
    <!-- 患者信息输入区 -->
    <el-card class="patient-input-card">
      <template #header>
        <h3>患者信息输入</h3>
      </template>
      <el-form :model="patientForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="患者年龄">
              <el-input-number v-model="patientForm.age" :min="0" :max="120" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="患者性别">
              <el-select v-model="patientForm.gender">
                <el-option label="男性" value="男性" />
                <el-option label="女性" value="女性" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="推荐数量">
              <el-input-number v-model="patientForm.max_recommendations" :min="1" :max="10" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="症状列表">
          <el-select v-model="patientForm.symptoms" multiple filterable allow-create>
            <el-option v-for="symptom in commonSymptoms" :key="symptom" :label="symptom" :value="symptom" />
          </el-select>
        </el-form-item>
        <el-form-item label="病情描述">
          <el-input 
            v-model="patientForm.patient_description" 
            type="textarea" 
            :rows="4" 
            placeholder="请详细描述患者的病情，包括症状、体征、病史等信息"
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 推荐方法选择 -->
    <el-card class="method-selection-card">
      <template #header>
        <h3>推荐方法选择</h3>
      </template>
      <el-row :gutter="20">
        <el-col :span="8" v-for="method in methods" :key="method.key">
          <el-card 
            :class="['method-card', { active: selectedMethods.includes(method.key) }]"
            @click="toggleMethod(method.key)"
          >
            <div class="method-icon">
              <el-icon :size="32"><component :is="method.icon" /></el-icon>
            </div>
            <h4>{{ method.title }}</h4>
            <p>{{ method.description }}</p>
            <el-button 
              :type="selectedMethods.includes(method.key) ? 'primary' : 'default'"
              :loading="loadingStates[method.key]"
              @click.stop="executeMethod(method.key)"
            >
              {{ loadingStates[method.key] ? '分析中...' : '开始分析' }}
            </el-button>
          </el-card>
        </el-col>
      </el-row>
    </el-card>

    <!-- 结果展示区 -->
    <el-card class="results-card" v-if="hasResults">
      <template #header>
        <h3>推荐结果对比</h3>
      </template>
      <el-tabs v-model="activeResultTab">
        <el-tab-pane 
          v-for="(result, method) in results" 
          :key="method"
          :label="getMethodTitle(method)"
          :name="method"
        >
          <RecommendationResult :result="result" :method="method" />
        </el-tab-pane>
        <el-tab-pane label="对比分析" name="comparison" v-if="Object.keys(results).length > 1">
          <MethodComparison :results="results" />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>
```

#### 3.2.2 推荐结果组件
```vue
<template>
  <div class="recommendation-result">
    <!-- 分析概览 -->
    <el-row :gutter="20" class="analysis-overview">
      <el-col :span="6">
        <el-statistic title="分析时间" :value="result.analysis_time_ms" suffix="ms" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="置信度" :value="result.confidence_score" :precision="2" suffix="%" />
      </el-col>
      <el-col :span="6">
        <el-statistic title="推荐数量" :value="result.recommendations.length" />
      </el-col>
      <el-col :span="6">
        <el-tag :type="getMethodType(method)">{{ result.method_description }}</el-tag>
      </el-col>
    </el-row>

    <!-- 推荐列表 -->
    <el-table :data="result.recommendations" class="recommendation-table">
      <el-table-column prop="rank" label="排名" width="80" align="center">
        <template #default="{ row }">
          <el-tag :type="getRankType(row.rank)" size="small">{{ row.rank }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="procedure_name" label="检查项目" min-width="200">
        <template #default="{ row }">
          <div class="procedure-info">
            <h4>{{ row.procedure_name }}</h4>
            <el-tag size="small">{{ row.modality }}</el-tag>
            <el-tag size="small" type="info">{{ row.panel_name }}</el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="appropriateness_rating" label="适宜性评分" width="120" align="center">
        <template #default="{ row }">
          <el-rate 
            v-model="row.appropriateness_rating" 
            :max="9" 
            disabled 
            show-score 
            text-color="#ff9900"
          />
        </template>
      </el-table-column>
      <el-table-column prop="evidence_level" label="证据等级" width="100" align="center">
        <template #default="{ row }">
          <el-tag :type="getEvidenceType(row.evidence_level)" size="small">
            {{ row.evidence_level }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="radiation_level" label="辐射水平" width="100" align="center">
        <template #default="{ row }">
          <RadiationIndicator :level="row.radiation_level" />
        </template>
      </el-table-column>
      <el-table-column prop="reasoning" label="推荐理由" min-width="300">
        <template #default="{ row }">
          <el-popover placement="top" :width="400" trigger="hover">
            <template #reference>
              <div class="reasoning-text">{{ row.reasoning }}</div>
            </template>
            <div class="reasoning-detail">
              <h4>详细推荐理由</h4>
              <p>{{ row.reasoning }}</p>
            </div>
          </el-popover>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" align="center">
        <template #default="{ row }">
          <el-button size="small" @click="viewDetail(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
```

### 3.3 向量检索模块

#### 3.3.1 综合检索页面
```vue
<template>
  <div class="vector-search">
    <!-- 搜索输入区 -->
    <el-card class="search-input-card">
      <el-form :model="searchForm" @submit.prevent="performSearch">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-input 
              v-model="searchForm.query_text"
              placeholder="请输入搜索关键词，支持中英文医学术语"
              size="large"
              clearable
            >
              <template #prepend>
                <el-icon><Search /></el-icon>
              </template>
              <template #append>
                <el-button type="primary" @click="performSearch" :loading="searching">
                  搜索
                </el-button>
              </template>
            </el-input>
          </el-col>
          <el-col :span="4">
            <el-input-number 
              v-model="searchForm.top_k" 
              :min="1" 
              :max="50" 
              placeholder="结果数量"
              size="large"
            />
          </el-col>
          <el-col :span="4">
            <el-input-number 
              v-model="searchForm.similarity_threshold" 
              :min="0" 
              :max="1" 
              :step="0.1" 
              placeholder="相似度阈值"
              size="large"
            />
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 搜索结果 -->
    <el-card class="search-results-card" v-if="searchResults">
      <template #header>
        <div class="results-header">
          <h3>搜索结果</h3>
          <div class="results-stats">
            <el-tag>总计: {{ searchResults.total_results }} 条</el-tag>
            <el-tag type="info">耗时: {{ searchResults.search_time_ms }}ms</el-tag>
          </div>
        </div>
      </template>
      
      <el-tabs v-model="activeTab">
        <el-tab-pane label="科室" name="panels" :badge="searchResults.panels.length">
          <SearchResultTable 
            :data="searchResults.panels" 
            type="panel"
            @view-detail="viewPanelDetail"
          />
        </el-tab-pane>
        <el-tab-pane label="主题" name="topics" :badge="searchResults.topics.length">
          <SearchResultTable 
            :data="searchResults.topics" 
            type="topic"
            @view-detail="viewTopicDetail"
          />
        </el-tab-pane>
        <el-tab-pane label="场景" name="scenarios" :badge="searchResults.scenarios.length">
          <SearchResultTable 
            :data="searchResults.scenarios" 
            type="scenario"
            @view-detail="viewScenarioDetail"
          />
        </el-tab-pane>
        <el-tab-pane label="项目" name="procedures" :badge="searchResults.procedures.length">
          <SearchResultTable 
            :data="searchResults.procedures" 
            type="procedure"
            @view-detail="viewProcedureDetail"
          />
        </el-tab-pane>
        <el-tab-pane label="推荐" name="recommendations" :badge="searchResults.recommendations.length">
          <SearchResultTable 
            :data="searchResults.recommendations" 
            type="recommendation"
            @view-detail="viewRecommendationDetail"
          />
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>
```

### 3.4 数据浏览模块

#### 3.4.1 层级浏览页面
```vue
<template>
  <div class="hierarchy-browse">
    <!-- 面包屑导航 -->
    <el-breadcrumb class="breadcrumb-nav">
      <el-breadcrumb-item @click="navigateToLevel('root')">首页</el-breadcrumb-item>
      <el-breadcrumb-item v-if="currentPanel" @click="navigateToLevel('panel')">
        {{ currentPanel.name_zh }}
      </el-breadcrumb-item>
      <el-breadcrumb-item v-if="currentTopic" @click="navigateToLevel('topic')">
        {{ currentTopic.name_zh }}
      </el-breadcrumb-item>
      <el-breadcrumb-item v-if="currentScenario">
        {{ currentScenario.description_zh.substring(0, 30) }}...
      </el-breadcrumb-item>
    </el-breadcrumb>

    <!-- 主内容区 -->
    <el-row :gutter="20">
      <!-- 左侧树形导航 -->
      <el-col :span="6">
        <el-card class="tree-nav-card">
          <template #header>
            <h4>数据层级导航</h4>
          </template>
          <el-tree 
            :data="treeData"
            :props="treeProps"
            node-key="id"
            :expand-on-click-node="false"
            @node-click="handleNodeClick"
            :current-node-key="currentNodeKey"
          >
            <template #default="{ node, data }">
              <div class="tree-node">
                <el-icon><component :is="getNodeIcon(data.type)" /></el-icon>
                <span class="node-label">{{ getNodeLabel(data) }}</span>
                <el-tag size="small" v-if="data.count">{{ data.count }}</el-tag>
              </div>
            </template>
          </el-tree>
        </el-card>
      </el-col>

      <!-- 右侧详情展示 -->
      <el-col :span="18">
        <el-card class="detail-card">
          <template #header>
            <div class="detail-header">
              <h3>{{ getDetailTitle() }}</h3>
              <div class="detail-actions">
                <el-button size="small" @click="refreshData">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
                <el-button size="small" @click="exportData">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </div>
          </template>

          <!-- 根据当前层级显示不同内容 -->
          <component 
            :is="getCurrentComponent()"
            :data="currentData"
            :loading="loading"
            @item-click="handleItemClick"
            @edit-item="handleEditItem"
          />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
```

## 4. 用户交互流程设计

### 4.1 智能推荐流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API
    participant V as 向量服务
    participant L as LLM服务

    U->>F: 输入患者信息
    F->>F: 表单验证
    U->>F: 选择推荐方法
    F->>A: POST /api/v1/three-methods/{method}
    A->>V: 向量检索
    A->>L: LLM分析（如需要）
    A->>F: 返回推荐结果
    F->>U: 展示推荐列表
    U->>F: 查看详细理由
    F->>U: 显示推荐详情
```

### 4.2 向量检索流程
```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API
    participant V as 向量服务
    participant D as 数据库

    U->>F: 输入搜索关键词
    F->>F: 输入验证
    F->>A: POST /api/v1/vector-search/comprehensive
    A->>V: 生成查询向量
    A->>D: 向量相似度搜索
    D->>A: 返回搜索结果
    A->>F: 格式化结果数据
    F->>U: 分类展示结果
    U->>F: 点击查看详情
    F->>A: GET /api/v1/{entity}/{id}
    A->>F: 返回详细信息
    F->>U: 显示详情弹窗
```

### 4.3 数据浏览流程
```mermaid
stateDiagram-v2
    [*] --> PanelList: 进入浏览页面
    PanelList --> TopicList: 选择科室
    TopicList --> ScenarioList: 选择主题
    ScenarioList --> ProcedureList: 选择场景
    ProcedureList --> RecommendationDetail: 选择项目
    
    PanelList --> PanelDetail: 查看科室详情
    TopicList --> TopicDetail: 查看主题详情
    ScenarioList --> ScenarioDetail: 查看场景详情
    ProcedureList --> ProcedureDetail: 查看项目详情
    
    PanelDetail --> PanelList: 返回列表
    TopicDetail --> TopicList: 返回列表
    ScenarioDetail --> ScenarioList: 返回列表
    ProcedureDetail --> ProcedureList: 返回列表
    RecommendationDetail --> ProcedureList: 返回列表
```

## 5. 数据可视化呈现方式

### 5.1 推荐结果可视化

#### 5.1.1 适宜性评分雷达图
```vue
<template>
  <div class="appropriateness-radar">
    <div ref="radarChart" class="radar-chart"></div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts'

const props = defineProps<{
  recommendations: RecommendationItem[]
}>()

const radarOption = computed(() => ({
  title: {
    text: '推荐项目适宜性对比',
    left: 'center'
  },
  radar: {
    indicator: props.recommendations.map(item => ({
      name: item.procedure_name.substring(0, 10),
      max: 9
    }))
  },
  series: [{
    type: 'radar',
    data: [{
      value: props.recommendations.map(item => item.appropriateness_rating),
      name: '适宜性评分'
    }]
  }]
}))
</script>
```

#### 5.1.2 方法对比热力图
```vue
<template>
  <div class="method-comparison-heatmap">
    <div ref="heatmapChart" class="heatmap-chart"></div>
  </div>
</template>

<script setup>
const heatmapOption = computed(() => ({
  title: {
    text: '三种推荐方法结果对比',
    left: 'center'
  },
  tooltip: {
    position: 'top',
    formatter: function (params) {
      return `${params.data[1]} - ${params.data[0]}<br/>适宜性评分: ${params.data[2]}`
    }
  },
  grid: {
    height: '50%',
    top: '10%'
  },
  xAxis: {
    type: 'category',
    data: ['向量法', 'LLM法', 'RAG法'],
    splitArea: {
      show: true
    }
  },
  yAxis: {
    type: 'category',
    data: props.procedures,
    splitArea: {
      show: true
    }
  },
  visualMap: {
    min: 1,
    max: 9,
    calculable: true,
    orient: 'horizontal',
    left: 'center',
    bottom: '15%'
  },
  series: [{
    name: '适宜性评分',
    type: 'heatmap',
    data: props.comparisonData,
    label: {
      show: true
    },
    emphasis: {
      itemStyle: {
        shadowBlur: 10,
        shadowColor: 'rgba(0, 0, 0, 0.5)'
      }
    }
  }]
}))
</script>
```

### 5.2 向量检索可视化

#### 5.2.1 相似度分布图
```vue
<template>
  <div class="similarity-distribution">
    <div ref="distributionChart" class="distribution-chart"></div>
  </div>
</template>

<script setup>
const distributionOption = computed(() => ({
  title: {
    text: '搜索结果相似度分布',
    left: 'center'
  },
  xAxis: {
    type: 'category',
    data: props.searchResults.map(item => item.name_zh || item.description_zh?.substring(0, 10))
  },
  yAxis: {
    type: 'value',
    name: '相似度',
    max: 1
  },
  series: [{
    data: props.searchResults.map(item => ({
      value: item.similarity_score,
      itemStyle: {
        color: getColorByScore(item.similarity_score)
      }
    })),
    type: 'bar',
    showBackground: true,
    backgroundStyle: {
      color: 'rgba(180, 180, 180, 0.2)'
    }
  }]
}))

function getColorByScore(score: number): string {
  if (score >= 0.8) return '#67C23A'
  if (score >= 0.6) return '#E6A23C'
  if (score >= 0.4) return '#F56C6C'
  return '#909399'
}
</script>
```

### 5.3 数据统计仪表板

#### 5.3.1 系统概览仪表板
```vue
<template>
  <div class="system-dashboard">
    <el-row :gutter="20">
      <!-- 数据统计卡片 -->
      <el-col :span="6" v-for="stat in dataStats" :key="stat.key">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon">
              <el-icon :size="32" :color="stat.color">
                <component :is="stat.icon" />
              </el-icon>
            </div>
            <div class="stat-info">
              <h3>{{ stat.value }}</h3>
              <p>{{ stat.title }}</p>
              <el-progress 
                :percentage="stat.coverage" 
                :color="stat.color"
                :show-text="false"
                :stroke-width="4"
              />
              <span class="coverage-text">向量覆盖率: {{ stat.coverage }}%</span>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 使用趋势图 -->
    <el-row :gutter="20" class="trend-charts">
      <el-col :span="12">
        <el-card>
          <template #header>
            <h4>搜索使用趋势</h4>
          </template>
          <div ref="searchTrendChart" class="trend-chart"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card>
          <template #header>
            <h4>推荐方法使用分布</h4>
          </template>
          <div ref="methodDistChart" class="dist-chart"></div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
```

## 6. 响应式设计规范

### 6.1 断点定义
```scss
// 响应式断点
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1600px
);

// 媒体查询混入
@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}
```

### 6.2 组件响应式适配
```vue
<template>
  <div class="responsive-container">
    <!-- 桌面端布局 -->
    <div class="desktop-layout" v-if="!isMobile">
      <el-row :gutter="20">
        <el-col :span="6">侧边栏</el-col>
        <el-col :span="18">主内容</el-col>
      </el-row>
    </div>
    
    <!-- 移动端布局 -->
    <div class="mobile-layout" v-else>
      <el-drawer v-model="drawerVisible" direction="ltr" size="80%">
        <!-- 移动端侧边栏 -->
      </el-drawer>
      <div class="mobile-content">
        <!-- 移动端主内容 -->
      </div>
      <div class="mobile-bottom-nav">
        <!-- 底部导航 -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { useBreakpoints } from '@vueuse/core'

const breakpoints = useBreakpoints({
  mobile: 768,
  tablet: 992,
  desktop: 1200
})

const isMobile = breakpoints.smaller('tablet')
const isTablet = breakpoints.between('tablet', 'desktop')
const isDesktop = breakpoints.greater('desktop')
</script>
```

### 6.3 表格响应式处理
```vue
<template>
  <div class="responsive-table">
    <!-- 桌面端表格 -->
    <el-table v-if="!isMobile" :data="tableData">
      <el-table-column prop="name" label="名称" />
      <el-table-column prop="description" label="描述" />
      <el-table-column prop="rating" label="评分" />
      <el-table-column prop="actions" label="操作" />
    </el-table>
    
    <!-- 移动端卡片列表 -->
    <div v-else class="mobile-card-list">
      <el-card v-for="item in tableData" :key="item.id" class="mobile-card">
        <div class="card-header">
          <h4>{{ item.name }}</h4>
          <el-rate v-model="item.rating" disabled size="small" />
        </div>
        <p class="card-description">{{ item.description }}</p>
        <div class="card-actions">
          <el-button size="small" @click="viewDetail(item)">详情</el-button>
          <el-button size="small" type="primary" @click="editItem(item)">编辑</el-button>
        </div>
      </el-card>
    </div>
  </div>
</template>
```

## 7. 技术实现要点

### 7.1 状态管理
```typescript
// stores/acrac.ts
import { defineStore } from 'pinia'

export const useAcracStore = defineStore('acrac', {
  state: () => ({
    // 当前用户信息
    currentUser: null as User | null,
    
    // 数据缓存
    panels: [] as Panel[],
    topics: [] as Topic[],
    scenarios: [] as ClinicalScenario[],
    procedures: [] as ProcedureDictionary[],
    
    // 搜索状态
    searchHistory: [] as string[],
    searchResults: null as SearchResults | null,
    
    // 推荐状态
    currentRecommendations: null as RecommendationResponse | null,
    comparisonResults: {} as Record<string, RecommendationResponse>,
    
    // UI状态
    loading: false,
    sidebarCollapsed: false,
    currentTheme: 'light' as 'light' | 'dark'
  }),
  
  getters: {
    isAuthenticated: (state) => !!state.currentUser,
    hasSearchResults: (state) => !!state.searchResults,
    totalDataCount: (state) => 
      state.panels.length + state.topics.length + 
      state.scenarios.length + state.procedures.length
  },
  
  actions: {
    async fetchPanels() {
      this.loading = true
      try {
        const response = await panelApi.getList()
        this.panels = response.data
      } finally {
        this.loading = false
      }
    },
    
    async performSearch(query: string) {
      this.loading = true
      try {
        const response = await searchApi.comprehensive({ query_text: query })
        this.searchResults = response.data
        this.addSearchHistory(query)
      } finally {
        this.loading = false
      }
    },
    
    async getRecommendations(method: string, request: RecommendationRequest) {
      this.loading = true
      try {
        const response = await recommendationApi[method](request)
        this.currentRecommendations = response.data
        this.comparisonResults[method] = response.data
      } finally {
        this.loading = false
      }
    }
  }
})
```

### 7.2 API封装
```typescript
// api/recommendation.ts
import { request } from '@/utils/request'

export const recommendationApi = {
  // 向量法推荐
  vectorMethod: (data: RecommendationRequest) => 
    request.post<RecommendationResponse>('/api/v1/three-methods/vector-method', data),
  
  // LLM法推荐
  llmMethod: (data: RecommendationRequest) => 
    request.post<RecommendationResponse>('/api/v1/three-methods/llm-method', data),
  
  // RAG法推荐
  ragMethod: (data: RecommendationRequest) => 
    request.post<RecommendationResponse>('/api/v1/three-methods/rag-method', data),
  
  // 方法对比
  compareAllMethods: (data: RecommendationRequest) => 
    request.post('/api/v1/three-methods/compare-all-methods', data),
  
  // 临床案例分析
  analyzeClinicalCase: (data: ClinicalAnalysisRequest) => 
    request.post<ClinicalAnalysisResponse>('/api/v1/intelligent-analysis/analyze-case', data)
}

// api/search.ts
export const searchApi = {
  // 综合检索
  comprehensive: (data: VectorSearchRequest) => 
    request.post<ComprehensiveSearchResponse>('/api/v1/vector-search/search/comprehensive', data),
  
  // 科室检索
  panels: (data: VectorSearchRequest) => 
    request.post<PanelSearchResult[]>('/api/v1/vector-search/search/panels', data),
  
  // 场景检索
  scenarios: (data: VectorSearchRequest) => 
    request.post<ScenarioSearchResult[]>('/api/v1/vector-search/search/scenarios', data),
  
  // 项目检索
  procedures: (data: VectorSearchRequest) => 
    request.post<ProcedureSearchResult[]>('/api/v1/vector-search/search/procedures', data),
  
  // 推荐检索
  recommendations: (data: VectorSearchRequest) => 
    request.post<RecommendationSearchResult[]>('/api/v1/vector-search/search/recommendations', data)
}
```

### 7.3 组件复用设计
```typescript
// components/common/DataTable.vue
<template>
  <div class="data-table">
    <el-table 
      :data="data" 
      :loading="loading"
      @selection-change="handleSelectionChange"
      @sort-change="handleSortChange"
    >
      <el-table-column 
        v-for="column in columns" 
        :key="column.prop"
        v-bind="column"
      >
        <template #default="scope" v-if="column.slot">
          <slot :name="column.slot" :row="scope.row" :index="scope.$index" />
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination 
      v-if="pagination"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="total"
      @current-change="handlePageChange"
      @size-change="handleSizeChange"
    />
  </div>
</template>

<script setup generic="T">
interface Column {
  prop: string
  label: string
  width?: number
  sortable?: boolean
  slot?: string
}

interface Props {
  data: T[]
  columns: Column[]
  loading?: boolean
  pagination?: boolean
  total?: number
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  pagination: true,
  total: 0
})

const emit = defineEmits<{
  selectionChange: [selection: T[]]
  sortChange: [sort: { prop: string; order: string }]
  pageChange: [page: number]
  sizeChange: [size: number]
}>()
</script>
```

## 8. 总结

本设计方案基于ACRAC系统的backend架构深度分析，提供了完整的前端UI实现框架：

### 8.1 核心特点
1. **数据驱动**：严格匹配backend的五表分离数据模型
2. **功能完整**：覆盖三种推荐方法、向量检索、数据浏览等核心功能
3. **交互友好**：提供直观的层级导航和智能搜索体验
4. **可视化丰富**：多种图表展示推荐结果和数据分析
5. **响应式设计**：适配桌面端、平板端、移动端多种设备

### 8.2 技术优势
1. **组件化架构**：高度复用的组件设计，便于维护和扩展
2. **状态管理**：统一的数据流管理，确保数据一致性
3. **API封装**：完整的接口封装，与backend API无缝对接
4. **类型安全**：TypeScript全覆盖，提供完整的类型定义

### 8.3 实施建议
1. **分阶段开发**：建议按模块优先级分阶段实施
2. **用户测试**：在开发过程中持续进行用户体验测试
3. **性能优化**：关注大数据量场景下的性能表现
4. **国际化支持**：预留中英文切换的技术架构

该方案确保了前端实现与backend能力的严格匹配，为ACRAC医疗智能推荐系统提供了完整、可行的前端解决方案。