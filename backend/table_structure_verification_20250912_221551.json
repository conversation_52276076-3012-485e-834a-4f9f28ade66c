[{"table_name": "evaluation_tasks", "exists_in_db": true, "model_columns_count": 27, "db_columns_count": 27, "missing_columns": [], "extra_columns": [], "type_mismatches": [], "nullable_mismatches": [], "is_consistent": true}, {"table_name": "scenario_results", "exists_in_db": true, "model_columns_count": 35, "db_columns_count": 35, "missing_columns": [], "extra_columns": [], "type_mismatches": [], "nullable_mismatches": [], "is_consistent": true}, {"table_name": "evaluation_metrics", "exists_in_db": true, "model_columns_count": 9, "db_columns_count": 9, "missing_columns": [], "extra_columns": [], "type_mismatches": [], "nullable_mismatches": [], "is_consistent": true}, {"table_name": "data_adapter_logs", "exists_in_db": true, "model_columns_count": 15, "db_columns_count": 15, "missing_columns": [], "extra_columns": [], "type_mismatches": [], "nullable_mismatches": [], "is_consistent": true}]