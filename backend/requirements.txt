# ACRAC Backend Requirements - V2.0
# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0.post1
python-multipart==0.0.6
python-dotenv==1.0.0

# Database and Vector Support
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
pgvector==0.2.3
alembic==1.12.1

# Data Processing
pandas==2.1.3
openpyxl==3.1.2
numpy==1.26.2

# API and Validation
pydantic==2.5.0
pydantic-settings==2.1.0
httpx==0.25.2

# Vector Embeddings and AI
sentence-transformers==2.2.2
torch==2.1.1
transformers==4.35.2
openai>=1.0.0

# Caching and Performance
redis==4.6.0
asyncpg==0.29.0

# Async Task Queue
celery==5.3.4
celery[redis]==5.3.4

# Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
flake8==6.1.0
mypy==1.7.1
pre-commit==3.5.0

# Monitoring and Logging
prometheus-client==0.19.0

# Additional utilities
requests==2.31.0
python-dateutil==2.8.2
pytz==2023.3