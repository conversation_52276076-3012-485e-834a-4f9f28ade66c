{"timestamp": "2025-09-12T22:14:22.943024", "database_info": {"version": "PostgreSQL 15.14 (Debian 15.14-1.pgdg12+1) on aarch64-unknown-linux-gnu, compiled by gcc (Debian 12.2.0-14+deb12u1) 12.2.0, 64-bit"}, "tables": {"clinical_recommendations": {"type": "BASE TABLE", "size": "247 MB", "record_count": 15970, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('clinical_recommendations_id_seq'::regclass)"}, {"name": "semantic_id", "type": "character varying", "nullable": false, "default": null}, {"name": "scenario_id", "type": "character varying", "nullable": true, "default": null}, {"name": "procedure_id", "type": "character varying", "nullable": true, "default": null}, {"name": "appropriateness_rating", "type": "integer", "nullable": true, "default": null}, {"name": "appropriateness_category", "type": "character varying", "nullable": true, "default": null}, {"name": "appropriateness_category_zh", "type": "character varying", "nullable": true, "default": null}, {"name": "reasoning_en", "type": "text", "nullable": true, "default": null}, {"name": "reasoning_zh", "type": "text", "nullable": true, "default": null}, {"name": "evidence_level", "type": "character varying", "nullable": true, "default": null}, {"name": "median_rating", "type": "double precision", "nullable": true, "default": null}, {"name": "rating_variance", "type": "double precision", "nullable": true, "default": null}, {"name": "consensus_level", "type": "character varying", "nullable": true, "default": null}, {"name": "adult_radiation_dose", "type": "character varying", "nullable": true, "default": null}, {"name": "pediatric_radiation_dose", "type": "character varying", "nullable": true, "default": null}, {"name": "contraindications", "type": "text", "nullable": true, "default": null}, {"name": "special_considerations", "type": "text", "nullable": true, "default": null}, {"name": "pregnancy_safety", "type": "character varying", "nullable": true, "default": null}, {"name": "is_generated", "type": "boolean", "nullable": true, "default": "false"}, {"name": "confidence_score", "type": "double precision", "nullable": true, "default": "1.0"}, {"name": "last_reviewed_date", "type": "date", "nullable": true, "default": null}, {"name": "reviewer_id", "type": "integer", "nullable": true, "default": null}, {"name": "is_active", "type": "boolean", "nullable": true, "default": "true"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "CURRENT_TIMESTAMP"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": true, "default": "CURRENT_TIMESTAMP"}, {"name": "embedding", "type": "USER-DEFINED", "nullable": true, "default": null}], "indexes": [{"name": "clinical_recommendations_pkey", "column": "id", "is_primary": true, "is_unique": true}, {"name": "clinical_recommendations_scenario_id_procedure_id_key", "column": "procedure_id", "is_primary": false, "is_unique": true}, {"name": "clinical_recommendations_scenario_id_procedure_id_key", "column": "scenario_id", "is_primary": false, "is_unique": true}, {"name": "clinical_recommendations_semantic_id_key", "column": "semantic_id", "is_primary": false, "is_unique": true}, {"name": "idx_recommendations_embedding", "column": "embedding", "is_primary": false, "is_unique": false}, {"name": "idx_recommendations_procedure", "column": "procedure_id", "is_primary": false, "is_unique": false}, {"name": "idx_recommendations_rating", "column": "appropriateness_rating", "is_primary": false, "is_unique": false}, {"name": "idx_recommendations_scenario", "column": "scenario_id", "is_primary": false, "is_unique": false}, {"name": "idx_recommendations_semantic_id", "column": "semantic_id", "is_primary": false, "is_unique": false}]}, "clinical_scenarios": {"type": "BASE TABLE", "size": "21 MB", "record_count": 1391, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('clinical_scenarios_id_seq'::regclass)"}, {"name": "semantic_id", "type": "character varying", "nullable": false, "default": null}, {"name": "panel_id", "type": "integer", "nullable": true, "default": null}, {"name": "topic_id", "type": "integer", "nullable": true, "default": null}, {"name": "description_en", "type": "text", "nullable": false, "default": null}, {"name": "description_zh", "type": "text", "nullable": false, "default": null}, {"name": "clinical_context", "type": "text", "nullable": true, "default": null}, {"name": "patient_population", "type": "character varying", "nullable": true, "default": null}, {"name": "risk_level", "type": "character varying", "nullable": true, "default": null}, {"name": "age_group", "type": "character varying", "nullable": true, "default": null}, {"name": "gender", "type": "character varying", "nullable": true, "default": null}, {"name": "pregnancy_status", "type": "character varying", "nullable": true, "default": null}, {"name": "urgency_level", "type": "character varying", "nullable": true, "default": null}, {"name": "symptom_category", "type": "character varying", "nullable": true, "default": null}, {"name": "is_active", "type": "boolean", "nullable": true, "default": "true"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "CURRENT_TIMESTAMP"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": true, "default": "CURRENT_TIMESTAMP"}, {"name": "embedding", "type": "USER-DEFINED", "nullable": true, "default": null}], "indexes": [{"name": "clinical_scenarios_pkey", "column": "id", "is_primary": true, "is_unique": true}, {"name": "clinical_scenarios_semantic_id_key", "column": "semantic_id", "is_primary": false, "is_unique": true}, {"name": "idx_scenarios_desc_unique", "column": "description_en", "is_primary": false, "is_unique": true}, {"name": "idx_scenarios_desc_unique", "column": "description_zh", "is_primary": false, "is_unique": true}, {"name": "idx_scenarios_desc_unique", "column": "topic_id", "is_primary": false, "is_unique": true}, {"name": "idx_scenarios_embedding", "column": "embedding", "is_primary": false, "is_unique": false}, {"name": "idx_scenarios_panel_topic", "column": "panel_id", "is_primary": false, "is_unique": false}, {"name": "idx_scenarios_panel_topic", "column": "topic_id", "is_primary": false, "is_unique": false}, {"name": "idx_scenarios_semantic_id", "column": "semantic_id", "is_primary": false, "is_unique": false}]}, "data_adapter_logs": {"type": "BASE TABLE", "size": "32 kB", "record_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('data_adapter_logs_id_seq'::regclass)"}, {"name": "scenario_id", "type": "character varying", "nullable": false, "default": null}, {"name": "task_id", "type": "character varying", "nullable": false, "default": null}, {"name": "input_format", "type": "character varying", "nullable": true, "default": null}, {"name": "output_format", "type": "character varying", "nullable": true, "default": null}, {"name": "adapter_version", "type": "character varying", "nullable": true, "default": null}, {"name": "raw_input_data", "type": "json", "nullable": true, "default": null}, {"name": "transformed_output_data", "type": "json", "nullable": true, "default": null}, {"name": "transformation_rules_applied", "type": "json", "nullable": true, "default": null}, {"name": "data_quality_score", "type": "double precision", "nullable": true, "default": null}, {"name": "processing_time_ms", "type": "integer", "nullable": true, "default": null}, {"name": "status", "type": "character varying", "nullable": true, "default": null}, {"name": "warnings", "type": "json", "nullable": true, "default": null}, {"name": "errors", "type": "json", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}], "indexes": [{"name": "data_adapter_logs_pkey", "column": "id", "is_primary": true, "is_unique": true}, {"name": "ix_data_adapter_logs_id", "column": "id", "is_primary": false, "is_unique": false}, {"name": "ix_data_adapter_logs_scenario_id", "column": "scenario_id", "is_primary": false, "is_unique": false}]}, "data_import_tasks": {"type": "BASE TABLE", "size": "24 kB", "record_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('data_import_tasks_id_seq'::regclass)"}, {"name": "user_id", "type": "integer", "nullable": false, "default": null}, {"name": "filename", "type": "character varying", "nullable": false, "default": null}, {"name": "file_size", "type": "integer", "nullable": true, "default": null}, {"name": "file_type", "type": "character varying", "nullable": true, "default": null}, {"name": "status", "type": "character varying", "nullable": true, "default": null}, {"name": "total_records", "type": "integer", "nullable": true, "default": null}, {"name": "processed_records", "type": "integer", "nullable": true, "default": null}, {"name": "success_records", "type": "integer", "nullable": true, "default": null}, {"name": "error_records", "type": "integer", "nullable": true, "default": null}, {"name": "error_details", "type": "json", "nullable": true, "default": null}, {"name": "started_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "completed_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}], "indexes": [{"name": "data_import_tasks_pkey", "column": "id", "is_primary": true, "is_unique": true}, {"name": "ix_data_import_tasks_id", "column": "id", "is_primary": false, "is_unique": false}]}, "data_update_history": {"type": "BASE TABLE", "size": "16 kB", "record_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('data_update_history_id_seq'::regclass)"}, {"name": "table_name", "type": "character varying", "nullable": false, "default": null}, {"name": "record_id", "type": "character varying", "nullable": false, "default": null}, {"name": "operation", "type": "character varying", "nullable": false, "default": null}, {"name": "old_data", "type": "jsonb", "nullable": true, "default": null}, {"name": "new_data", "type": "jsonb", "nullable": true, "default": null}, {"name": "user_id", "type": "integer", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "CURRENT_TIMESTAMP"}], "indexes": [{"name": "data_update_history_pkey", "column": "id", "is_primary": true, "is_unique": true}]}, "evaluation_metrics": {"type": "BASE TABLE", "size": "24 kB", "record_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('evaluation_metrics_id_seq'::regclass)"}, {"name": "task_id", "type": "character varying", "nullable": false, "default": null}, {"name": "metric_name", "type": "character varying", "nullable": false, "default": null}, {"name": "metric_value", "type": "double precision", "nullable": false, "default": null}, {"name": "metric_category", "type": "character varying", "nullable": true, "default": null}, {"name": "calculation_method", "type": "character varying", "nullable": true, "default": null}, {"name": "sample_size", "type": "integer", "nullable": true, "default": null}, {"name": "confidence_interval", "type": "json", "nullable": true, "default": null}, {"name": "measured_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}], "indexes": [{"name": "evaluation_metrics_pkey", "column": "id", "is_primary": true, "is_unique": true}, {"name": "ix_evaluation_metrics_id", "column": "id", "is_primary": false, "is_unique": false}]}, "evaluation_tasks": {"type": "BASE TABLE", "size": "32 kB", "record_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('evaluation_tasks_id_seq'::regclass)"}, {"name": "task_id", "type": "character varying", "nullable": false, "default": null}, {"name": "task_name", "type": "character varying", "nullable": false, "default": null}, {"name": "description", "type": "text", "nullable": true, "default": null}, {"name": "status", "type": "character varying", "nullable": true, "default": null}, {"name": "file_path", "type": "character varying", "nullable": true, "default": null}, {"name": "file_name", "type": "character varying", "nullable": true, "default": null}, {"name": "file_size", "type": "integer", "nullable": true, "default": null}, {"name": "total_scenarios", "type": "integer", "nullable": true, "default": null}, {"name": "completed_scenarios", "type": "integer", "nullable": true, "default": null}, {"name": "failed_scenarios", "type": "integer", "nullable": true, "default": null}, {"name": "progress_percentage", "type": "double precision", "nullable": true, "default": null}, {"name": "evaluation_config", "type": "json", "nullable": true, "default": null}, {"name": "avg_faithfulness", "type": "double precision", "nullable": true, "default": null}, {"name": "avg_answer_relevancy", "type": "double precision", "nullable": true, "default": null}, {"name": "avg_context_precision", "type": "double precision", "nullable": true, "default": null}, {"name": "avg_context_recall", "type": "double precision", "nullable": true, "default": null}, {"name": "avg_overall_score", "type": "double precision", "nullable": true, "default": null}, {"name": "started_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "completed_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "estimated_completion", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "error_message", "type": "text", "nullable": true, "default": null}, {"name": "error_details", "type": "json", "nullable": true, "default": null}, {"name": "created_by", "type": "character varying", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}, {"name": "celery_task_id", "type": "character varying", "nullable": true, "default": null}], "indexes": [{"name": "evaluation_tasks_pkey", "column": "id", "is_primary": true, "is_unique": true}, {"name": "ix_evaluation_tasks_id", "column": "id", "is_primary": false, "is_unique": false}, {"name": "ix_evaluation_tasks_task_id", "column": "task_id", "is_primary": false, "is_unique": true}]}, "excel_evaluation_data": {"type": "BASE TABLE", "size": "48 kB", "record_count": 6, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('excel_evaluation_data_id_seq'::regclass)"}, {"name": "task_id", "type": "character varying", "nullable": false, "default": null}, {"name": "filename", "type": "character varying", "nullable": false, "default": null}, {"name": "question", "type": "text", "nullable": false, "default": null}, {"name": "ground_truth", "type": "text", "nullable": false, "default": null}, {"name": "contexts", "type": "json", "nullable": true, "default": null}, {"name": "answer", "type": "text", "nullable": true, "default": null}, {"name": "faithfulness", "type": "double precision", "nullable": true, "default": null}, {"name": "answer_relevancy", "type": "double precision", "nullable": true, "default": null}, {"name": "context_precision", "type": "double precision", "nullable": true, "default": null}, {"name": "context_recall", "type": "double precision", "nullable": true, "default": null}, {"name": "status", "type": "character varying", "nullable": true, "default": null}, {"name": "error_message", "type": "text", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}], "indexes": [{"name": "excel_evaluation_data_pkey", "column": "id", "is_primary": true, "is_unique": true}, {"name": "ix_excel_evaluation_data_id", "column": "id", "is_primary": false, "is_unique": false}]}, "inference_logs": {"type": "BASE TABLE", "size": "24 kB", "record_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('inference_logs_id_seq'::regclass)"}, {"name": "user_id", "type": "integer", "nullable": true, "default": null}, {"name": "query_text", "type": "text", "nullable": false, "default": null}, {"name": "query_language", "type": "character varying", "nullable": true, "default": null}, {"name": "inference_method", "type": "character varying", "nullable": true, "default": null}, {"name": "result", "type": "json", "nullable": true, "default": null}, {"name": "confidence_score", "type": "double precision", "nullable": true, "default": null}, {"name": "success", "type": "boolean", "nullable": false, "default": null}, {"name": "error_message", "type": "text", "nullable": true, "default": null}, {"name": "execution_time", "type": "double precision", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}], "indexes": [{"name": "inference_logs_pkey", "column": "id", "is_primary": true, "is_unique": true}, {"name": "ix_inference_logs_id", "column": "id", "is_primary": false, "is_unique": false}]}, "panels": {"type": "BASE TABLE", "size": "992 kB", "record_count": 13, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('panels_id_seq'::regclass)"}, {"name": "semantic_id", "type": "character varying", "nullable": false, "default": null}, {"name": "name_en", "type": "character varying", "nullable": false, "default": null}, {"name": "name_zh", "type": "character varying", "nullable": false, "default": null}, {"name": "description", "type": "text", "nullable": true, "default": null}, {"name": "is_active", "type": "boolean", "nullable": true, "default": "true"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "CURRENT_TIMESTAMP"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": true, "default": "CURRENT_TIMESTAMP"}, {"name": "embedding", "type": "USER-DEFINED", "nullable": true, "default": null}], "indexes": [{"name": "idx_panels_embedding", "column": "embedding", "is_primary": false, "is_unique": false}, {"name": "idx_panels_name_unique", "column": "name_en", "is_primary": false, "is_unique": true}, {"name": "idx_panels_name_unique", "column": "name_zh", "is_primary": false, "is_unique": true}, {"name": "idx_panels_semantic_id", "column": "semantic_id", "is_primary": false, "is_unique": false}, {"name": "panels_pkey", "column": "id", "is_primary": true, "is_unique": true}, {"name": "panels_semantic_id_key", "column": "semantic_id", "is_primary": false, "is_unique": true}]}, "procedure_dictionary": {"type": "BASE TABLE", "size": "16 MB", "record_count": 1053, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('procedure_dictionary_id_seq'::regclass)"}, {"name": "semantic_id", "type": "character varying", "nullable": false, "default": null}, {"name": "name_en", "type": "character varying", "nullable": false, "default": null}, {"name": "name_zh", "type": "character varying", "nullable": false, "default": null}, {"name": "modality", "type": "character varying", "nullable": true, "default": null}, {"name": "body_part", "type": "character varying", "nullable": true, "default": null}, {"name": "contrast_used", "type": "boolean", "nullable": true, "default": "false"}, {"name": "radiation_level", "type": "character varying", "nullable": true, "default": null}, {"name": "exam_duration", "type": "integer", "nullable": true, "default": null}, {"name": "preparation_required", "type": "boolean", "nullable": true, "default": "false"}, {"name": "standard_code", "type": "character varying", "nullable": true, "default": null}, {"name": "icd10_code", "type": "character varying", "nullable": true, "default": null}, {"name": "cpt_code", "type": "character varying", "nullable": true, "default": null}, {"name": "description_en", "type": "text", "nullable": true, "default": null}, {"name": "description_zh", "type": "text", "nullable": true, "default": null}, {"name": "is_active", "type": "boolean", "nullable": true, "default": "true"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "CURRENT_TIMESTAMP"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": true, "default": "CURRENT_TIMESTAMP"}, {"name": "embedding", "type": "USER-DEFINED", "nullable": true, "default": null}], "indexes": [{"name": "idx_procedures_embedding", "column": "embedding", "is_primary": false, "is_unique": false}, {"name": "idx_procedures_modality", "column": "modality", "is_primary": false, "is_unique": false}, {"name": "idx_procedures_name_unique", "column": "name_en", "is_primary": false, "is_unique": true}, {"name": "idx_procedures_name_unique", "column": "name_zh", "is_primary": false, "is_unique": true}, {"name": "idx_procedures_semantic_id", "column": "semantic_id", "is_primary": false, "is_unique": false}, {"name": "procedure_dictionary_pkey", "column": "id", "is_primary": true, "is_unique": true}, {"name": "procedure_dictionary_semantic_id_key", "column": "semantic_id", "is_primary": false, "is_unique": true}]}, "procedures": {"type": "BASE TABLE", "size": "18 MB", "record_count": 15974, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('procedures_id_seq'::regclass)"}, {"name": "variant_id", "type": "integer", "nullable": false, "default": null}, {"name": "name_en", "type": "character varying", "nullable": false, "default": null}, {"name": "name_zh", "type": "character varying", "nullable": false, "default": null}, {"name": "recommendation_en", "type": "text", "nullable": true, "default": null}, {"name": "recommendation_zh", "type": "text", "nullable": true, "default": null}, {"name": "appropriateness_category", "type": "character varying", "nullable": true, "default": null}, {"name": "appropriateness_category_zh", "type": "character varying", "nullable": true, "default": null}, {"name": "rating", "type": "double precision", "nullable": true, "default": null}, {"name": "median", "type": "double precision", "nullable": true, "default": null}, {"name": "soe", "type": "character varying", "nullable": true, "default": null}, {"name": "adult_rrl", "type": "character varying", "nullable": true, "default": null}, {"name": "peds_rrl", "type": "character varying", "nullable": true, "default": null}, {"name": "is_generated", "type": "boolean", "nullable": true, "default": null}, {"name": "is_active", "type": "boolean", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}, {"name": "embedding", "type": "USER-DEFINED", "nullable": true, "default": null}], "indexes": [{"name": "ix_procedures_id", "column": "id", "is_primary": false, "is_unique": false}, {"name": "procedures_pkey", "column": "id", "is_primary": true, "is_unique": true}]}, "rules": {"type": "BASE TABLE", "size": "24 kB", "record_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('rules_id_seq'::regclass)"}, {"name": "rule_name", "type": "character varying", "nullable": false, "default": null}, {"name": "rule_content", "type": "json", "nullable": false, "default": null}, {"name": "description", "type": "text", "nullable": true, "default": null}, {"name": "status", "type": "character varying", "nullable": true, "default": null}, {"name": "priority", "type": "integer", "nullable": true, "default": null}, {"name": "created_by", "type": "integer", "nullable": false, "default": null}, {"name": "approved_by", "type": "integer", "nullable": true, "default": null}, {"name": "version", "type": "integer", "nullable": true, "default": null}, {"name": "is_active", "type": "boolean", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}], "indexes": [{"name": "ix_rules_id", "column": "id", "is_primary": false, "is_unique": false}, {"name": "rules_pkey", "column": "id", "is_primary": true, "is_unique": true}]}, "scenario_results": {"type": "BASE TABLE", "size": "40 kB", "record_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('scenario_results_id_seq'::regclass)"}, {"name": "scenario_id", "type": "character varying", "nullable": false, "default": null}, {"name": "task_id", "type": "character varying", "nullable": false, "default": null}, {"name": "question_number", "type": "character varying", "nullable": true, "default": null}, {"name": "clinical_scenario", "type": "text", "nullable": true, "default": null}, {"name": "standard_answer", "type": "character varying", "nullable": true, "default": null}, {"name": "rag_question", "type": "text", "nullable": true, "default": null}, {"name": "rag_answer", "type": "text", "nullable": true, "default": null}, {"name": "rag_contexts", "type": "json", "nullable": true, "default": null}, {"name": "rag_trace_data", "type": "json", "nullable": true, "default": null}, {"name": "adapted_data", "type": "json", "nullable": true, "default": null}, {"name": "faithfulness_score", "type": "double precision", "nullable": true, "default": null}, {"name": "answer_relevancy_score", "type": "double precision", "nullable": true, "default": null}, {"name": "context_precision_score", "type": "double precision", "nullable": true, "default": null}, {"name": "context_recall_score", "type": "double precision", "nullable": true, "default": null}, {"name": "overall_score", "type": "double precision", "nullable": true, "default": null}, {"name": "ragas_evaluation_details", "type": "json", "nullable": true, "default": null}, {"name": "evaluation_metadata", "type": "json", "nullable": true, "default": null}, {"name": "status", "type": "character varying", "nullable": true, "default": null}, {"name": "processing_stage", "type": "character varying", "nullable": true, "default": null}, {"name": "inference_started_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "inference_completed_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "evaluation_started_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "evaluation_completed_at", "type": "timestamp without time zone", "nullable": true, "default": null}, {"name": "inference_duration_ms", "type": "integer", "nullable": true, "default": null}, {"name": "evaluation_duration_ms", "type": "integer", "nullable": true, "default": null}, {"name": "total_duration_ms", "type": "integer", "nullable": true, "default": null}, {"name": "error_message", "type": "text", "nullable": true, "default": null}, {"name": "error_stage", "type": "character varying", "nullable": true, "default": null}, {"name": "error_details", "type": "json", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}, {"name": "inference_task_id", "type": "character varying", "nullable": true, "default": null}, {"name": "parsing_task_id", "type": "character varying", "nullable": true, "default": null}, {"name": "evaluation_task_id", "type": "character varying", "nullable": true, "default": null}], "indexes": [{"name": "ix_scenario_results_id", "column": "id", "is_primary": false, "is_unique": false}, {"name": "ix_scenario_results_scenario_id", "column": "scenario_id", "is_primary": false, "is_unique": false}, {"name": "ix_scenario_results_task_id", "column": "task_id", "is_primary": false, "is_unique": false}, {"name": "scenario_results_pkey", "column": "id", "is_primary": true, "is_unique": true}]}, "topics": {"type": "BASE TABLE", "size": "4928 kB", "record_count": 285, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('topics_id_seq'::regclass)"}, {"name": "semantic_id", "type": "character varying", "nullable": false, "default": null}, {"name": "panel_id", "type": "integer", "nullable": true, "default": null}, {"name": "name_en", "type": "character varying", "nullable": false, "default": null}, {"name": "name_zh", "type": "character varying", "nullable": false, "default": null}, {"name": "description", "type": "text", "nullable": true, "default": null}, {"name": "is_active", "type": "boolean", "nullable": true, "default": "true"}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "CURRENT_TIMESTAMP"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": true, "default": "CURRENT_TIMESTAMP"}, {"name": "embedding", "type": "USER-DEFINED", "nullable": true, "default": null}], "indexes": [{"name": "idx_topics_embedding", "column": "embedding", "is_primary": false, "is_unique": false}, {"name": "idx_topics_name_unique", "column": "name_en", "is_primary": false, "is_unique": true}, {"name": "idx_topics_name_unique", "column": "name_zh", "is_primary": false, "is_unique": true}, {"name": "idx_topics_name_unique", "column": "panel_id", "is_primary": false, "is_unique": true}, {"name": "idx_topics_panel_id", "column": "panel_id", "is_primary": false, "is_unique": false}, {"name": "idx_topics_semantic_id", "column": "semantic_id", "is_primary": false, "is_unique": false}, {"name": "topics_pkey", "column": "id", "is_primary": true, "is_unique": true}, {"name": "topics_semantic_id_key", "column": "semantic_id", "is_primary": false, "is_unique": true}]}, "users": {"type": "BASE TABLE", "size": "40 kB", "record_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('users_id_seq'::regclass)"}, {"name": "username", "type": "character varying", "nullable": false, "default": null}, {"name": "email", "type": "character varying", "nullable": false, "default": null}, {"name": "hashed_password", "type": "character varying", "nullable": false, "default": null}, {"name": "full_name", "type": "character varying", "nullable": true, "default": null}, {"name": "role", "type": "character varying", "nullable": true, "default": null}, {"name": "is_active", "type": "boolean", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}], "indexes": [{"name": "ix_users_email", "column": "email", "is_primary": false, "is_unique": true}, {"name": "ix_users_id", "column": "id", "is_primary": false, "is_unique": false}, {"name": "ix_users_username", "column": "username", "is_primary": false, "is_unique": true}, {"name": "users_pkey", "column": "id", "is_primary": true, "is_unique": true}]}, "variants": {"type": "BASE TABLE", "size": "560 kB", "record_count": 1391, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('variants_id_seq'::regclass)"}, {"name": "topic_id", "type": "integer", "nullable": false, "default": null}, {"name": "description_en", "type": "text", "nullable": false, "default": null}, {"name": "description_zh", "type": "text", "nullable": false, "default": null}, {"name": "clinical_context", "type": "text", "nullable": true, "default": null}, {"name": "is_active", "type": "boolean", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}, {"name": "updated_at", "type": "timestamp without time zone", "nullable": true, "default": "now()"}, {"name": "embedding", "type": "USER-DEFINED", "nullable": true, "default": null}], "indexes": [{"name": "ix_variants_id", "column": "id", "is_primary": false, "is_unique": false}, {"name": "variants_pkey", "column": "id", "is_primary": true, "is_unique": true}]}, "vector_search_logs": {"type": "BASE TABLE", "size": "16 kB", "record_count": 0, "columns": [{"name": "id", "type": "integer", "nullable": false, "default": "nextval('vector_search_logs_id_seq'::regclass)"}, {"name": "query_text", "type": "text", "nullable": false, "default": null}, {"name": "query_type", "type": "character varying", "nullable": true, "default": null}, {"name": "search_vector", "type": "USER-DEFINED", "nullable": true, "default": null}, {"name": "results_count", "type": "integer", "nullable": true, "default": null}, {"name": "search_time_ms", "type": "integer", "nullable": true, "default": null}, {"name": "user_id", "type": "integer", "nullable": true, "default": null}, {"name": "created_at", "type": "timestamp without time zone", "nullable": true, "default": "CURRENT_TIMESTAMP"}], "indexes": [{"name": "vector_search_logs_pkey", "column": "id", "is_primary": true, "is_unique": true}]}}, "summary": {"total_tables": 18, "total_records": 36083, "ragas_tables_found": {"evaluation_tasks": true, "scenario_results": true, "evaluation_metrics": true}}}