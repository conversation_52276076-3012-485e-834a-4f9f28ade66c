import os
from pydantic_settings import BaseSettings
from typing import List, Optional
import os
from pathlib import Path

class Settings(BaseSettings):
    # Project info
    PROJECT_NAME: str = "ACRAC System"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    
    # Database - 连接到Docker容器中的PostgreSQL
    # 修复：优先使用环境变量中的DATABASE_URL，如果没有则使用默认值
    DATABASE_URL: str = os.getenv("DATABASE_URL", "********************************************/acrac_db")
    DATABASE_POOL_SIZE: int = 20
    DATABASE_MAX_OVERFLOW: int = 0
    
    # PostgreSQL 配置
    PGHOST: str = os.getenv("PGHOST", "postgres")  # 修复：使用docker服务名而不是localhost
    PGPORT: str = os.getenv("PGPORT", "5432")
    PGDATABASE: str = os.getenv("PGDATABASE", "acrac_db")
    PGUSER: str = os.getenv("PGUSER", "postgres")
    PGPASSWORD: str = os.getenv("PGPASSWORD", "password")
    
    # Redis
    REDIS_URL: str = os.getenv("REDIS_URL", "redis://redis:6379/0")  # 修复：使用docker服务名而不是localhost
    
    # Security
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-here-please-change-in-production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:5173", "http://localhost:5174", "http://localhost:3000"]
    
    # Celery
    CELERY_BROKER_URL: str = os.getenv("CELERY_BROKER_URL", "redis://redis:6379/1")  # 修复：使用docker服务名而不是localhost
    CELERY_RESULT_BACKEND: str = os.getenv("CELERY_RESULT_BACKEND", "redis://redis:6379/2")  # 修复：使用docker服务名而不是localhost
    
    # Embedding Model (统一使用嵌入配置)
    EMBEDDING_MODEL_TYPE: str = "bge-m3"
    EMBEDDING_MODEL_NAME: str = "bge-m3:latest"
    EMBEDDING_DIMENSION: int = 1024
    
    # SiliconFlow API Configuration
    SILICONFLOW_API_KEY: str = os.getenv("SILICONFLOW_API_KEY", "sk-ybghruztazvtitpwrityokshmckxkwflviwpuvseqopmxfze")
    SILICONFLOW_EMBEDDING_MODEL: str = os.getenv("SILICONFLOW_EMBEDDING_MODEL", "BAAI/bge-m3")
    SILICONFLOW_LLM_MODEL: str = os.getenv("SILICONFLOW_LLM_MODEL", "Qwen/Qwen2.5-32B-Instruct")
    SILICONFLOW_BASE_URL: str = os.getenv("SILICONFLOW_BASE_URL", "https://api.siliconflow.cn/v1")
    
    # Reranker Configuration
    RERANKER_MODEL: str = os.getenv("RERANKER_MODEL", "BAAI/bge-reranker-v2-m3")
    
    # OpenAI API Configuration
    OPENAI_API_KEY: str = os.getenv("OPENAI_API_KEY", "")
    
    # RAG 配置
    VECTOR_SIMILARITY_THRESHOLD: float = 0.6
    DEBUG_MODE: bool = True
    
    # 提示词配置参数
    RAG_TOP_SCENARIOS: int = 2
    RAG_TOP_RECOMMENDATIONS_PER_SCENARIO: int = 3
    RAG_SHOW_REASONING: bool = True
    
    # File Upload
    MAX_UPLOAD_SIZE: int = 104857600  # 100MB
    ALLOWED_EXTENSIONS: List[str] = [".xlsx", ".xls", ".csv", ".json"]
    
    # Logging
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/acrac.log"
    
    # Development
    DEBUG: bool = True
    RELOAD: bool = True

    # Rules Engine (default disabled)
    RULES_ENABLED: bool = False
    RULES_AUDIT_ONLY: bool = True
    RULES_CONFIG_PATH: str = str(Path(__file__).resolve().parents[2] / "config" / "rules_packs.json")
    
    class Config:
        # 统一从 backend/.env 加载
        env_file = str(Path(__file__).resolve().parents[2] / ".env")
        case_sensitive = True

# Create settings instance
settings = Settings()

# Ensure log directory exists
log_dir = Path(settings.LOG_FILE).parent
log_dir.mkdir(exist_ok=True)