from fastapi import APIRouter, HTTPException, UploadFile, File
from pydantic import BaseModel, Field
from typing import Optional, Dict, Any
from pathlib import Path
import subprocess
import time
import os
import re
from app.core.config import settings

router = APIRouter()

UPLOAD_DIR = Path(__file__).resolve().parents[5] / 'backend' / 'uploads'
SCRIPTS_DIR = Path(__file__).resolve().parents[5] / 'backend' / 'scripts'


class ImportRequest(BaseModel):
    csv_path: Optional[str] = Field(None, description='服务器上的CSV路径（若已上传）')
    mode: str = Field('clear', description='导入模式: clear(清空重建)/add(追加)')
    embedding_model: Optional[str] = Field(None, description='SiliconFlow embedding model id, e.g., BAAI/bge-m3')
    llm_model: Optional[str] = Field(None, description='LLM model id used in service')


class ImportResponse(BaseModel):
    started: bool
    log_path: Optional[str] = None
    command: Optional[str] = None
    metrics: Optional[Dict[str, Any]] = None


class ModelsConfig(BaseModel):
    embedding_model: Optional[str] = None
    llm_model: Optional[str] = None
    reranker_model: Optional[str] = None
    base_url: Optional[str] = None
    siliconflow_api_key: Optional[str] = None
    openai_api_key: Optional[str] = None

@router.get('/validate', summary='数据合规性校验（表计数、向量覆盖、孤儿推荐）')
async def validate_data() -> Dict[str, Any]:
    try:
        import psycopg2
        cfg = {
            'host': os.getenv('PGHOST','localhost'),
            'port': int(os.getenv('PGPORT','5432')),
            'database': os.getenv('PGDATABASE','acrac_db'),
            'user': os.getenv('PGUSER','postgres'),
            'password': os.getenv('PGPASSWORD','password'),
        }
        conn = psycopg2.connect(**cfg)
        cur = conn.cursor()
        def cnt(sql: str) -> int:
            cur.execute(sql);
            return cur.fetchone()[0]
        tables = {
            'panels': cnt('SELECT COUNT(*) FROM panels'),
            'topics': cnt('SELECT COUNT(*) FROM topics'),
            'clinical_scenarios': cnt('SELECT COUNT(*) FROM clinical_scenarios'),
            'procedure_dictionary': cnt('SELECT COUNT(*) FROM procedure_dictionary'),
            'clinical_recommendations': cnt('SELECT COUNT(*) FROM clinical_recommendations'),
        }
        coverage: Dict[str, int] = {}
        for t in ['panels','topics','clinical_scenarios','procedure_dictionary','clinical_recommendations']:
            try:
                cur.execute(f"SELECT COUNT(embedding) FROM {t}")
                coverage[t] = cur.fetchone()[0]
            except Exception:
                coverage[t] = 0
        cur.execute(
            """
            SELECT COUNT(*) FROM clinical_recommendations cr
            WHERE cr.scenario_id NOT IN (SELECT semantic_id FROM clinical_scenarios)
               OR cr.procedure_id NOT IN (SELECT semantic_id FROM procedure_dictionary)
            """
        )
        orphan = cur.fetchone()[0]
        conn.close()
        return { 'tables': tables, 'embedding_coverage': coverage, 'orphan_recommendations': orphan }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post('/upload', summary='上传CSV文件')
async def upload_csv(file: UploadFile = File(...)) -> Dict[str, Any]:
    try:
        UPLOAD_DIR.mkdir(parents=True, exist_ok=True)
        ts = time.strftime('%Y%m%d_%H%M%S')
        dest = UPLOAD_DIR / f'{ts}_{file.filename}'
        with dest.open('wb') as f:
            content = await file.read()
            f.write(content)
        return { 'ok': True, 'path': str(dest) }
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post('/import', response_model=ImportResponse, summary='从CSV导入/重建数据（同步执行，可能耗时）')
async def import_csv(req: ImportRequest) -> ImportResponse:
    try:
        # Enforce presence of API key to avoid building with random embeddings
        if not (os.getenv('SILICONFLOW_API_KEY') or settings.SILICONFLOW_API_KEY):
            raise HTTPException(status_code=400, detail='SILICONFLOW_API_KEY 未配置。为保证向量准确性，请先在 backend/.env 设置该Key。')
        csv_path = req.csv_path
        if not csv_path:
            raise HTTPException(status_code=400, detail='csv_path 必填（先上传或提供服务器绝对路径）')
        csv = Path(csv_path)
        if not csv.exists():
            raise HTTPException(status_code=400, detail='csv_path 不存在')
        log_dir = Path(__file__).resolve().parents[5] / 'backend' / 'logs'
        log_dir.mkdir(parents=True, exist_ok=True)
        ts = time.strftime('%Y%m%d_%H%M%S')
        log_path = log_dir / f'import_{ts}.log'

        # Build command
        script = SCRIPTS_DIR / 'build_acrac_from_csv_siliconflow.py'
        if not script.exists():
            raise HTTPException(status_code=500, detail='构建脚本不存在')
        action = 'build'
        args = [ 'python', str(script), action, '--csv-file', str(csv) ]
        # clear: create schema; add: skip schema
        if req.mode == 'add':
            args.append('--skip-schema')
        # env overrides
        env = os.environ.copy()
        if req.embedding_model:
            env['SILICONFLOW_EMBEDDING_MODEL'] = req.embedding_model
        if req.llm_model:
            env['SILICONFLOW_LLM_MODEL'] = req.llm_model

        with log_path.open('wb') as logf:
            proc = subprocess.Popen(args, stdout=logf, stderr=logf, env=env, cwd=str(SCRIPTS_DIR))
            proc.wait()
        # Quick compliance metrics
        metrics: Dict[str, Any] = {}
        try:
            import psycopg2
            cfg = {
                'host': os.getenv('PGHOST','localhost'),
                'port': int(os.getenv('PGPORT','5432')),
                'database': os.getenv('PGDATABASE','acrac_db'),
                'user': os.getenv('PGUSER','postgres'),
                'password': os.getenv('PGPASSWORD','password'),
            }
            conn = psycopg2.connect(**cfg)
            cur = conn.cursor()
            def cnt(sql):
                cur.execute(sql)
                return cur.fetchone()[0]
            metrics['tables'] = {
                'panels': cnt('SELECT COUNT(*) FROM panels'),
                'topics': cnt('SELECT COUNT(*) FROM topics'),
                'clinical_scenarios': cnt('SELECT COUNT(*) FROM clinical_scenarios'),
                'procedure_dictionary': cnt('SELECT COUNT(*) FROM procedure_dictionary'),
                'clinical_recommendations': cnt('SELECT COUNT(*) FROM clinical_recommendations'),
            }
            cov = {}
            for t in ['panels','topics','clinical_scenarios','procedure_dictionary','clinical_recommendations']:
                try:
                    cur.execute(f"SELECT COUNT(embedding) FROM {t}")
                    cov[t] = cur.fetchone()[0]
                except Exception:
                    cov[t] = 0
            metrics['embedding_coverage'] = cov
            cur.execute(
                """
                SELECT COUNT(*) FROM clinical_recommendations cr
                WHERE cr.scenario_id NOT IN (SELECT semantic_id FROM clinical_scenarios)
                   OR cr.procedure_id NOT IN (SELECT semantic_id FROM procedure_dictionary)
                """
            )
            metrics['orphan_recommendations'] = cur.fetchone()[0]
            conn.close()
        except Exception:
            metrics = {}
        return ImportResponse(started=True, log_path=str(log_path), command=' '.join(args), metrics=metrics)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# —— 模型配置 ——
@router.get('/models/config', summary='查看模型配置（当前进程/env）')
async def get_models_config() -> Dict[str, Any]:
    def has(k: str) -> bool:
        return bool(os.getenv(k))
    return {
        'embedding_model': os.getenv('SILICONFLOW_EMBEDDING_MODEL', ''),
        'llm_model': os.getenv('SILICONFLOW_LLM_MODEL', ''),
        'reranker_model': os.getenv('RERANKER_MODEL', 'BAAI/bge-reranker-v2-m3'),
        'base_url': os.getenv('SILICONFLOW_BASE_URL', 'https://api.siliconflow.cn/v1'),
        'keys': {
            'siliconflow_api_key': has('SILICONFLOW_API_KEY'),
            'openai_api_key': has('OPENAI_API_KEY'),
        },
        'providers': ['siliconflow','openai','local']
    }


def _update_env(text: str, key: str, value: Optional[str]) -> str:
    pattern = re.compile(rf'^{re.escape(key)}\s*=.*$', re.M)
    if value is None:
        return pattern.sub('', text)
    line = f'{key}={value}'
    if pattern.search(text):
        return pattern.sub(line, text)
    if not text.endswith('\n'):
        text += '\n'
    return text + line + '\n'


@router.post('/models/config', summary='更新模型配置（写入.env并设置当前进程env）')
async def set_models_config(cfg: ModelsConfig) -> Dict[str, Any]:
    try:
        if cfg.embedding_model is not None: os.environ['SILICONFLOW_EMBEDDING_MODEL'] = cfg.embedding_model
        if cfg.llm_model is not None: os.environ['SILICONFLOW_LLM_MODEL'] = cfg.llm_model
        if cfg.reranker_model is not None: os.environ['RERANKER_MODEL'] = cfg.reranker_model
        if cfg.base_url is not None: os.environ['SILICONFLOW_BASE_URL'] = cfg.base_url
        if cfg.siliconflow_api_key is not None: os.environ['SILICONFLOW_API_KEY'] = cfg.siliconflow_api_key
        if cfg.openai_api_key is not None: os.environ['OPENAI_API_KEY'] = cfg.openai_api_key

        env_path = Path(__file__).resolve().parents[5] / 'backend' / '.env'
        text = env_path.read_text(encoding='utf-8') if env_path.exists() else ''
        for k, v in [
            ('SILICONFLOW_EMBEDDING_MODEL', cfg.embedding_model),
            ('SILICONFLOW_LLM_MODEL', cfg.llm_model),
            ('RERANKER_MODEL', cfg.reranker_model),
            ('SILICONFLOW_BASE_URL', cfg.base_url),
            ('SILICONFLOW_API_KEY', cfg.siliconflow_api_key),
            ('OPENAI_API_KEY', cfg.openai_api_key),
        ]:
            text = _update_env(text, k, v)
        env_path.write_text(text, encoding='utf-8')
        return {'ok': True, 'requires_restart': True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.post('/models/reload', summary='重载RAG+LLM服务实例（应用部分配置）')
async def reload_rag_service() -> Dict[str, Any]:
    try:
        from app.services.rag_llm_recommendation_service import RAGLLMRecommendationService
        from app.services import rag_llm_recommendation_service as mod
        mod.rag_llm_service = RAGLLMRecommendationService()
        return {'ok': True}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
