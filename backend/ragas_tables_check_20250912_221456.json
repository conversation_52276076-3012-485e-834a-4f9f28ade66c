{"evaluation_tasks": {"exists": true, "columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": "nextval('evaluation_tasks_id_seq'::regclass)"}, {"name": "task_id", "type": "VARCHAR(50)", "nullable": false, "default": null}, {"name": "task_name", "type": "VARCHAR(255)", "nullable": false, "default": null}, {"name": "description", "type": "TEXT", "nullable": true, "default": null}, {"name": "status", "type": "VARCHAR(20)", "nullable": true, "default": null}, {"name": "file_path", "type": "VARCHAR(500)", "nullable": true, "default": null}, {"name": "file_name", "type": "VARCHAR(255)", "nullable": true, "default": null}, {"name": "file_size", "type": "INTEGER", "nullable": true, "default": null}, {"name": "total_scenarios", "type": "INTEGER", "nullable": true, "default": null}, {"name": "completed_scenarios", "type": "INTEGER", "nullable": true, "default": null}, {"name": "failed_scenarios", "type": "INTEGER", "nullable": true, "default": null}, {"name": "progress_percentage", "type": "DOUBLE PRECISION", "nullable": true, "default": null}, {"name": "evaluation_config", "type": "JSON", "nullable": true, "default": null}, {"name": "avg_faithfulness", "type": "DOUBLE PRECISION", "nullable": true, "default": null}, {"name": "avg_answer_relevancy", "type": "DOUBLE PRECISION", "nullable": true, "default": null}, {"name": "avg_context_precision", "type": "DOUBLE PRECISION", "nullable": true, "default": null}, {"name": "avg_context_recall", "type": "DOUBLE PRECISION", "nullable": true, "default": null}, {"name": "avg_overall_score", "type": "DOUBLE PRECISION", "nullable": true, "default": null}, {"name": "started_at", "type": "TIMESTAMP", "nullable": true, "default": null}, {"name": "completed_at", "type": "TIMESTAMP", "nullable": true, "default": null}, {"name": "estimated_completion", "type": "TIMESTAMP", "nullable": true, "default": null}, {"name": "error_message", "type": "TEXT", "nullable": true, "default": null}, {"name": "error_details", "type": "JSON", "nullable": true, "default": null}, {"name": "created_by", "type": "VARCHAR(100)", "nullable": true, "default": null}, {"name": "created_at", "type": "TIMESTAMP", "nullable": true, "default": "now()"}, {"name": "updated_at", "type": "TIMESTAMP", "nullable": true, "default": "now()"}, {"name": "celery_task_id", "type": "VARCHAR(255)", "nullable": true, "default": null}], "primary_key": ["id"], "foreign_keys": [], "indexes": [{"name": "ix_evaluation_tasks_id", "unique": false, "column_names": ["id"], "include_columns": [], "dialect_options": {"postgresql_include": []}}, {"name": "ix_evaluation_tasks_task_id", "unique": true, "column_names": ["task_id"], "include_columns": [], "dialect_options": {"postgresql_include": []}}], "record_count": 0}, "scenario_results": {"exists": true, "columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": "nextval('scenario_results_id_seq'::regclass)"}, {"name": "scenario_id", "type": "VARCHAR(50)", "nullable": false, "default": null}, {"name": "task_id", "type": "VARCHAR(50)", "nullable": false, "default": null}, {"name": "question_number", "type": "VARCHAR(20)", "nullable": true, "default": null}, {"name": "clinical_scenario", "type": "TEXT", "nullable": true, "default": null}, {"name": "standard_answer", "type": "VARCHAR(500)", "nullable": true, "default": null}, {"name": "rag_question", "type": "TEXT", "nullable": true, "default": null}, {"name": "rag_answer", "type": "TEXT", "nullable": true, "default": null}, {"name": "rag_contexts", "type": "JSON", "nullable": true, "default": null}, {"name": "rag_trace_data", "type": "JSON", "nullable": true, "default": null}, {"name": "adapted_data", "type": "JSON", "nullable": true, "default": null}, {"name": "faithfulness_score", "type": "DOUBLE PRECISION", "nullable": true, "default": null}, {"name": "answer_relevancy_score", "type": "DOUBLE PRECISION", "nullable": true, "default": null}, {"name": "context_precision_score", "type": "DOUBLE PRECISION", "nullable": true, "default": null}, {"name": "context_recall_score", "type": "DOUBLE PRECISION", "nullable": true, "default": null}, {"name": "overall_score", "type": "DOUBLE PRECISION", "nullable": true, "default": null}, {"name": "ragas_evaluation_details", "type": "JSON", "nullable": true, "default": null}, {"name": "evaluation_metadata", "type": "JSON", "nullable": true, "default": null}, {"name": "status", "type": "VARCHAR(20)", "nullable": true, "default": null}, {"name": "processing_stage", "type": "VARCHAR(50)", "nullable": true, "default": null}, {"name": "inference_started_at", "type": "TIMESTAMP", "nullable": true, "default": null}, {"name": "inference_completed_at", "type": "TIMESTAMP", "nullable": true, "default": null}, {"name": "evaluation_started_at", "type": "TIMESTAMP", "nullable": true, "default": null}, {"name": "evaluation_completed_at", "type": "TIMESTAMP", "nullable": true, "default": null}, {"name": "inference_duration_ms", "type": "INTEGER", "nullable": true, "default": null}, {"name": "evaluation_duration_ms", "type": "INTEGER", "nullable": true, "default": null}, {"name": "total_duration_ms", "type": "INTEGER", "nullable": true, "default": null}, {"name": "error_message", "type": "TEXT", "nullable": true, "default": null}, {"name": "error_stage", "type": "VARCHAR(50)", "nullable": true, "default": null}, {"name": "error_details", "type": "JSON", "nullable": true, "default": null}, {"name": "created_at", "type": "TIMESTAMP", "nullable": true, "default": "now()"}, {"name": "updated_at", "type": "TIMESTAMP", "nullable": true, "default": "now()"}, {"name": "inference_task_id", "type": "VARCHAR(255)", "nullable": true, "default": null}, {"name": "parsing_task_id", "type": "VARCHAR(255)", "nullable": true, "default": null}, {"name": "evaluation_task_id", "type": "VARCHAR(255)", "nullable": true, "default": null}], "primary_key": ["id"], "foreign_keys": [{"name": "scenario_results_task_id_fkey", "constrained_columns": ["task_id"], "referred_schema": null, "referred_table": "evaluation_tasks", "referred_columns": ["task_id"], "options": {"ondelete": "CASCADE"}, "comment": null}], "indexes": [{"name": "ix_scenario_results_id", "unique": false, "column_names": ["id"], "include_columns": [], "dialect_options": {"postgresql_include": []}}, {"name": "ix_scenario_results_scenario_id", "unique": false, "column_names": ["scenario_id"], "include_columns": [], "dialect_options": {"postgresql_include": []}}, {"name": "ix_scenario_results_task_id", "unique": false, "column_names": ["task_id"], "include_columns": [], "dialect_options": {"postgresql_include": []}}], "record_count": 0}, "evaluation_metrics": {"exists": true, "columns": [{"name": "id", "type": "INTEGER", "nullable": false, "default": "nextval('evaluation_metrics_id_seq'::regclass)"}, {"name": "task_id", "type": "VARCHAR(50)", "nullable": false, "default": null}, {"name": "metric_name", "type": "VARCHAR(100)", "nullable": false, "default": null}, {"name": "metric_value", "type": "DOUBLE PRECISION", "nullable": false, "default": null}, {"name": "metric_category", "type": "VARCHAR(50)", "nullable": true, "default": null}, {"name": "calculation_method", "type": "VARCHAR(100)", "nullable": true, "default": null}, {"name": "sample_size", "type": "INTEGER", "nullable": true, "default": null}, {"name": "confidence_interval", "type": "JSON", "nullable": true, "default": null}, {"name": "measured_at", "type": "TIMESTAMP", "nullable": true, "default": "now()"}], "primary_key": ["id"], "foreign_keys": [{"name": "evaluation_metrics_task_id_fkey", "constrained_columns": ["task_id"], "referred_schema": null, "referred_table": "evaluation_tasks", "referred_columns": ["task_id"], "options": {"ondelete": "CASCADE"}, "comment": null}], "indexes": [{"name": "ix_evaluation_metrics_id", "unique": false, "column_names": ["id"], "include_columns": [], "dialect_options": {"postgresql_include": []}}], "record_count": 0}}