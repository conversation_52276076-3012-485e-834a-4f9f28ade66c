# ACRAC 环境配置文件
# 复制此文件为 .env 并填入实际配置

# SiliconFlow API 配置
SILICONFLOW_API_KEY=your_api_key_here
SILICONFLOW_EMBEDDING_MODEL=BAAI/bge-m3

# 数据库配置
DATABASE_URL=postgresql://postgres:password@localhost:5432/acrac_db
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=acrac_db
POSTGRES_USER=postgres
POSTGRES_PASSWORD=password

# 应用配置
SECRET_KEY=your_secret_key_here
DEBUG=True
LOG_LEVEL=INFO

# 向量配置
EMBEDDING_DIMENSION=1024
VECTOR_INDEX_LISTS=100
SEARCH_LIMIT=10

# API配置
API_V1_STR=/api/v1
PROJECT_NAME=ACRAC
VERSION=1.0.0
