# 医疗系统专用依赖包

# 核心依赖
fastapi==0.104.1
uvicorn[standard]==0.24.0
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
pgvector==0.2.4

# 向量化和机器学习
sentence-transformers==2.2.2
transformers==4.35.2
torch==2.1.1
numpy==1.24.3
scikit-learn==1.3.2

# HTTP客户端
requests==2.31.0
aiohttp==3.9.1

# 配置管理
pydantic-settings==2.1.0

# BGE中文模型 (推荐)
# BGE-Large-ZH: 1024维，中文优化，医疗场景最佳选择
# BGE-Base-ZH: 768维，平衡性能和速度
# 模型会自动从Hugging Face Hub下载，无需额外安装

# 中文医疗模型 (可选，根据需要安装)
# 如果需要使用m3e-base模型，需要额外安装：
# pip install m3e-base
# 或者使用Hugging Face Hub：
# pip install huggingface_hub

# 向量数据库客户端 (根据需要选择)
# Qdrant (推荐)
qdrant-client==1.6.9

# Weaviate (可选)
# weaviate-client==3.25.3

# Milvus (可选)
# pymilvus==2.3.4

# 数据处理
pandas==2.1.3
openpyxl==3.1.2
python-multipart==0.0.6

# 异步任务处理
celery==5.3.4
redis==5.0.1

# 国际化和本地化
python-i18n==0.3.9

# 日志和监控
loguru==0.7.2

# 开发和测试
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# 其他工具
python-dotenv==1.0.0
pydantic==2.5.0
pydantic-settings==2.1.0
