#!/usr/bin/env python3
"""
正确的RAGAS评估解决方案
解决IndexError和NaN值问题的完整实现
"""

import os
import json
import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional
import logging
import sys
from pathlib import Path
import warnings
warnings.filterwarnings("ignore")

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from datasets import Dataset
    from ragas import evaluate
    from ragas.metrics import (
        Faithfulness,
        AnswerRelevancy,
        ContextPrecision,
        ContextRecall,
    )
    from ragas.llms import LangchainLLMWrapper
    from ragas.embeddings import LangchainEmbeddingsWrapper
    from langchain_openai import ChatOpenAI, OpenAIEmbeddings
    RAGAS_AVAILABLE = True
except ImportError as e:
    logger.error(f"RAGAS导入失败: {e}")
    RAGAS_AVAILABLE = False
    sys.exit(1)

class CorrectRAGASEvaluator:
    """正确的RAGAS评估器实现"""
    
    def __init__(self):
        self.setup_environment()
        self.setup_models()
        
    def setup_environment(self):
        """设置环境变量"""
        try:
            env_path = Path(__file__).parent.parent / ".env"
            from dotenv import load_dotenv
            load_dotenv(env_path)
            logger.info("✅ 环境变量加载成功")
        except Exception as e:
            logger.warning(f"环境变量加载失败: {e}")
    
    def setup_models(self):
        """设置评估模型"""
        try:
            # 使用SiliconFlow API配置
            self.llm = ChatOpenAI(
                model="deepseek-ai/DeepSeek-R1",
                openai_api_base="https://api.siliconflow.cn/v1",
                openai_api_key=os.getenv("SILICONFLOW_API_KEY"),
                temperature=0.1,
                max_tokens=1000
            )
            
            self.embeddings = OpenAIEmbeddings(
                model="BAAI/bge-large-zh-v1.5",
                openai_api_base="https://api.siliconflow.cn/v1",
                openai_api_key=os.getenv("SILICONFLOW_API_KEY")
            )
            
            # 包装为RAGAS兼容格式
            self.evaluator_llm = LangchainLLMWrapper(self.llm)
            self.evaluator_embeddings = LangchainEmbeddingsWrapper(self.embeddings)
            
            logger.info("✅ 模型初始化成功")
            
        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            raise
    
    def prepare_ragas_dataset(self, test_data: List[Dict[str, Any]]) -> Dataset:
        """准备RAGAS兼容的数据集格式"""
        
        questions = []
        answers = []
        contexts = []
        ground_truths = []
        
        for item in test_data:
            try:
                # 确保所有必需字段存在
                question = item.get("question", "")
                answer = item.get("answer", "")
                context_list = item.get("contexts", [])
                ground_truth = item.get("ground_truth", "")
                
                # 清理和验证数据
                if not question or not answer:
                    logger.warning(f"跳过无效数据: {item}")
                    continue
                
                # 确保上下文是列表
                if isinstance(context_list, str):
                    context_list = [context_list]
                
                questions.append(question)
                answers.append(answer)
                contexts.append(context_list)
                ground_truths.append(ground_truth)
                
            except Exception as e:
                logger.error(f"处理数据项时出错: {e}")
                continue
        
        return Dataset.from_dict({
            "question": questions,
            "answer": answers,
            "contexts": contexts,
            "ground_truth": ground_truths
        })
    
    def safe_evaluate_sample(self, sample_data: Dict[str, Any]) -> Dict[str, float]:
        """安全地评估单个样本"""
        
        try:
            # 准备单个样本的数据集
            dataset = self.prepare_ragas_dataset([sample_data])
            
            if len(dataset) == 0:
                logger.warning("数据集为空")
                return {
                    "faithfulness": 0.0,
                    "answer_relevancy": 0.0,
                    "context_precision": 0.0,
                    "context_recall": 0.0
                }
            
            # 定义指标
            metrics = [
                Faithfulness(llm=self.evaluator_llm),
                AnswerRelevancy(llm=self.evaluator_llm, embeddings=self.evaluator_embeddings),
                ContextPrecision(llm=self.evaluator_llm),
                ContextRecall(llm=self.evaluator_llm)
            ]
            
            # 执行评估
            result = evaluate(
                dataset=dataset,
                metrics=metrics,
                llm=self.evaluator_llm,
                embeddings=self.evaluator_embeddings
            )
            
            # 处理结果
            result_df = result.to_pandas()
            
            # 安全地提取数值，处理NaN
            scores = {}
            for metric_name in ["faithfulness", "answer_relevancy", "context_precision", "context_recall"]:
                try:
                    value = result_df[metric_name].iloc[0]
                    scores[metric_name] = float(value) if pd.notna(value) else 0.0
                except (IndexError, KeyError):
                    scores[metric_name] = 0.0
            
            return scores
            
        except Exception as e:
            logger.error(f"评估样本失败: {e}")
            return {
                "faithfulness": 0.0,
                "answer_relevancy": 0.0,
                "context_precision": 0.0,
                "context_recall": 0.0
            }
    
    def create_test_sample(self) -> Dict[str, Any]:
        """创建测试样本"""
        return {
            "question": "35岁女性，头痛3天伴恶心呕吐，推荐什么影像学检查？",
            "answer": "推荐头部CT平扫作为首选检查，可以快速排除颅内出血等急性病变。如果CT结果阴性但症状持续，建议进一步行MRI检查。",
            "contexts": [
                "对于急性头痛患者，头部CT是首选的影像学检查方法，可以快速识别颅内出血、占位性病变等。",
                "CT检查对于急性颅内病变的敏感性高，是急诊科头痛患者的标准检查流程。",
                "MRI检查在软组织对比度方面优于CT，适用于CT阴性但症状持续的患者。"
            ],
            "ground_truth": "头部CT平扫"
        }
    
    def run_correct_evaluation(self) -> Dict[str, Any]:
        """运行正确的完整评估"""
        
        logger.info("🚀 开始正确的RAGAS评估...")
        
        # 创建测试样本
        test_sample = self.create_test_sample()
        
        # 评估单个样本
        scores = self.safe_evaluate_sample(test_sample)
        
        # 计算综合评分
        valid_scores = [v for v in scores.values() if v > 0]
        overall_score = np.mean(valid_scores) if valid_scores else 0.0
        
        result = {
            "sample": test_sample,
            "ragas_scores": scores,
            "overall_score": overall_score,
            "evaluation_status": "success",
            "timestamp": pd.Timestamp.now().isoformat()
        }
        
        logger.info("✅ 评估完成")
        logger.info(f"各项评分: {scores}")
        logger.info(f"综合评分: {overall_score:.3f}")
        
        return result
    
    def save_result(self, result: Dict[str, Any], filename: str = None):
        """保存评估结果"""
        if filename is None:
            filename = f"correct_ragas_result_{pd.Timestamp.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        filepath = Path(__file__).parent / filename
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        
        logger.info(f"结果已保存: {filepath}")

def main():
    """主函数"""
    if not RAGAS_AVAILABLE:
        logger.error("RAGAS不可用，请先安装依赖")
        return
    
    try:
        evaluator = CorrectRAGASEvaluator()
        result = evaluator.run_correct_evaluation()
        evaluator.save_result(result)
        
        # 打印最终结果
        print("\n" + "="*50)
        print("🎯 正确RAGAS评估结果")
        print("="*50)
        print(f"忠实度 (Faithfulness): {result['ragas_scores']['faithfulness']:.3f}")
        print(f"答案相关性 (Answer Relevancy): {result['ragas_scores']['answer_relevancy']:.3f}")
        print(f"上下文精确度 (Context Precision): {result['ragas_scores']['context_precision']:.3f}")
        print(f"上下文召回率 (Context Recall): {result['ragas_scores']['context_recall']:.3f}")
        print(f"综合评分: {result['overall_score']:.3f}")
        print("="*50)
        
    except Exception as e:
        logger.error(f"评估失败: {e}")
        raise

if __name__ == "__main__":
    main()