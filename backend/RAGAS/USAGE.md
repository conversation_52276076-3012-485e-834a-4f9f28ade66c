# RAGAS官方评测系统使用说明

## 🎯 系统目标

本系统基于RAGAS官方框架，对医疗RAG+LLM系统进行客观、准确的评测，确保评测结果的可信度。

## 📋 使用步骤

### 1. 环境准备

确保已安装所有依赖包：

```bash
pip install -r requirements.txt
```

### 2. API密钥配置

在项目根目录的 `backend/.env` 文件中配置SiliconFlow API密钥：

```env
SILICONFLOW_API_KEY=your_actual_api_key_here
```

### 3. 运行评测

#### 方法一：使用运行脚本（推荐）

```bash
./run_evaluation.sh
```

#### 方法二：直接运行Python脚本

```bash
# 先运行测试
python test_ragas_evaluation.py

# 再运行评测
python official_ragas_evaluation.py
```

## ⚙️ 配置参数

### 评测样本数量

默认评测前5个样本，如需评测更多样本，请修改 [official_ragas_evaluation.py](file:///Users/<USER>/git_project_vscode/09_medical/ACRAC-web/backend/RAGAS/official_ragas_evaluation.py) 中的 `max_samples` 参数：

```python
# 在 main() 函数中修改
report = await evaluator.run_evaluation(max_samples=50)  # 评测50个样本
```

### RAG系统参数

如需调整RAG系统的参数，请修改 [official_ragas_evaluation.py](file:///Users/<USER>/git_project_vscode/09_medical/ACRAC-web/backend/RAGAS/official_ragas_evaluation.py) 中的 `call_rag_llm_api` 方法中的payload：

```python
payload = {
    "clinical_query": clinical_scenario,
    "top_scenarios": 3,  # 场景top数量
    "top_recommendations_per_scenario": 5,  # 每个场景下检查项目top数量
    "show_reasoning": True,  # 推荐理由显示开关
    "similarity_threshold": 0.6,  # 向量相似度阈值
    "debug_mode": True,
    "include_raw_data": True
}
```

## 📊 评测报告解读

评测完成后会生成JSON格式的报告文件，文件名格式为：`official_ragas_evaluation_report_YYYYMMDD_HHMMSS.json`

### 报告结构

1. **evaluation_metadata**: 评测元数据
   - total_samples: 评测样本总数
   - evaluation_date: 评测日期
   - framework: 使用的评测框架
   - api_key_source: API密钥来源

2. **ragas_scores_statistics**: 各项指标统计
   - mean: 平均值
   - std: 标准差
   - min: 最小值
   - max: 最大值

3. **overall_performance**: 总体性能
   - mean_overall_score: 平均总体得分
   - score_distribution: 得分分布

4. **detailed_results**: 详细结果
   - 每个样本的详细评测结果

5. **optimization_insights**: 优化建议
   - 基于评测结果的系统优化建议

## 📈 评测指标说明

### 核心指标

| 指标 | 说明 | 目标值 |
|------|------|--------|
| Faithfulness | 忠实度 | ≥0.8 |
| Answer Relevancy | 答案相关性 | ≥0.8 |
| Context Precision | 上下文精确度 | ≥0.7 |
| Context Recall | 上下文召回率 | ≥0.7 |
| Accuracy | 准确率 | ≥0.8 |

### 总体评分

总体评分是各指标的加权平均值：
- Faithfulness: 25%
- Answer Relevancy: 25%
- Context Precision: 20%
- Context Recall: 20%
- Accuracy: 10%

## 🔍 故障排除

### API密钥相关问题

1. **错误信息**: "未找到SiliconFlow API密钥"
   - **解决方案**: 检查 `backend/.env` 文件是否存在，并确认已配置 `SILICONFLOW_API_KEY`

2. **错误信息**: "API调用失败"
   - **解决方案**: 检查API密钥是否正确，网络连接是否正常

### 依赖包相关问题

1. **错误信息**: "ModuleNotFoundError"
   - **解决方案**: 运行 `pip install -r requirements.txt` 安装所有依赖包

### 模型相关问题

1. **错误信息**: "模型连接失败"
   - **解决方案**: 检查网络连接和SiliconFlow服务状态

## 📞 技术支持

如遇到任何问题，请：

1. 检查系统日志中的错误信息
2. 确认环境配置是否正确
3. 联系项目维护人员
4. 查看RAGAS官方文档: https://docs.ragas.io/
