#!/usr/bin/env python3
"""
Trace 5 cases end-to-end via the live API and print intermediate steps:
- recall list, rerank list, mode, prompt length
- LLM parsed output, optional RAGAS scores (if ground_truth available)

Requires backend server running on http://127.0.0.1:8001
"""
import os
import time
import json
import requests
import pandas as pd
from pathlib import Path

API = os.getenv("RAG_API_URL", "http://127.0.0.1:8002/api/v1/acrac/rag-llm/intelligent-recommendation")
DATASET = os.getenv("ACRAC_EVAL_DATASET")
if not DATASET:
    # default to repo root Excel used in evaluator
    DATASET = str(Path(__file__).resolve().parents[2] / "影像测试样例-0318-1.xlsx")

def main():
    df = pd.read_excel(DATASET)
    df = df.head(5)
    out = []
    for i, row in df.iterrows():
        qid = int(row['题号'])
        query = str(row['临床场景'])
        gt = str(row['首选检查项目（标准化）']).strip('* ')
        payload = {
            "clinical_query": query,
            "top_scenarios": 3,
            "top_recommendations_per_scenario": 3,
            "show_reasoning": True,
            "similarity_threshold": 0.6,
            "debug_mode": True,
            "include_raw_data": True,
            "compute_ragas": True,
            "ground_truth": gt
        }
        print(f"\n=== Case #{qid} ===")
        print("Query:", query)
        print("GT:   ", gt)
        try:
            r = requests.post(API, json=payload, timeout=120)
        except Exception as e:
            print("API error:", e)
            continue
        if r.status_code != 200:
            print("Status:", r.status_code)
            print(r.text)
            continue
        obj = r.json()
        # 优先使用API返回的trace；若后端未透传trace，则从响应构造一个兼容的trace，避免空输出
        tr = obj.get('trace') or {}
        if not tr:
            scenarios = obj.get('scenarios') or []
            # 构造 recall 与 rerank 视图
            rec_list = []
            for s in scenarios:
                rec_list.append({
                    'id': s.get('semantic_id'),
                    'similarity': s.get('similarity'),
                    'panel': s.get('panel_name'),
                    'topic': s.get('topic_name'),
                    '_rerank_score': s.get('_rerank_score')
                })
            # rerank基于_srerank_score排序；若不存在则按similarity降序
            rr = sorted(rec_list, key=lambda x: (x.get('_rerank_score') is None, -(x.get('_rerank_score') or 0.0), -(x.get('similarity') or 0.0)))
            # 兼容trace结构
            tr = {
                'recall_scenarios': rec_list,
                'rerank_scenarios': rr,
                'final_prompt': None,  # 可能不可用
                'llm_parsed': obj.get('llm_recommendations') or {}
            }
            # 从debug_info补充prompt长度等
            dbg = obj.get('debug_info') or {}
            if not tr['final_prompt'] and isinstance(dbg.get('step_6_prompt_preview'), str):
                tr['final_prompt'] = dbg.get('step_6_prompt_preview')
        # Print recall
        rec = tr.get('recall_scenarios') or []
        print("Recall (id,sim):", [(s.get('id'), round((s.get('similarity') or 0.0), 3)) for s in rec])
        # Print rerank
        rr = tr.get('rerank_scenarios') or []
        print("Rerank (id,score):", [(s.get('id'), round((s.get('_rerank_score') or 0.0), 3)) for s in rr[:5]])
        # Mode + prompt
        print("Mode:", "no-RAG" if obj.get('is_low_similarity_mode') else "RAG")
        # 尝试打印prompt长度；若仅有长度数值，直接输出；否则计算字符串长度
        prm = tr.get('final_prompt') or ""
        if not prm and isinstance((obj.get('debug_info') or {}).get('step_6_prompt_length'), (int, float)):
            print("Prompt length:", int((obj['debug_info']['step_6_prompt_length'])))
        else:
            print("Prompt length:", len(prm))
        # LLM parsed
        parsed = tr.get('llm_parsed') or {}
        recs = parsed.get('recommendations') or []
        print("LLM recs:", [(r.get('procedure_name'), r.get('appropriateness_rating')) for r in recs])
        # RAGAS
        if 'ragas_scores' in tr:
            print("RAGAS:", tr['ragas_scores'])
        else:
            print("RAGAS: not computed or error:", tr.get('ragas_error'))
        out.append({"question_id": qid, "trace": tr})
        # pacing to avoid rate limits
        time.sleep(1.0)
    # save raw traces
    ts = time.strftime('%Y%m%d_%H%M%S')
    out_path = f"trace_5_cases_{ts}.json"
    Path(out_path).write_text(json.dumps(out, ensure_ascii=False, indent=2), encoding='utf-8')
    print("\nSaved:", out_path)

if __name__ == '__main__':
    main()
