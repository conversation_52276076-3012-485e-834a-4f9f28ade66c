# RAGAS官方评测系统

基于RAGAS官方框架的医疗RAG+LLM评测系统，使用真正的RAGAS评测方法。

## 📋 系统特点

1. **官方框架**: 使用RAGAS官方指标进行评测
2. **真实LLM**: 通过SiliconFlow调用真实的LLM进行评测
3. **环境配置**: 从.env文件安全加载API密钥
4. **全面指标**: 包含Faithfulness、Answer Relevancy、Context Precision、Context Recall等核心指标
5. **详细报告**: 生成完整的评测报告和优化建议

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置环境变量

确保在项目根目录的 `backend/.env` 文件中配置了SiliconFlow API密钥：

```env
SILICONFLOW_API_KEY=your_actual_api_key_here
```

### 3. 运行评测

```bash
python official_ragas_evaluation.py
```

## 📊 评测指标说明

### Faithfulness (忠实度)
衡量LLM生成的答案与检索上下文的一致性。通过LLM判断答案中的每个陈述是否能从检索到的上下文中推断出来。

### Answer Relevancy (答案相关性)
衡量生成的答案与用户查询的相关性。通过计算答案与查询之间的语义相似度来评估。

### Context Precision (上下文精确度)
衡量检索到的上下文的相关性。评估检索到的上下文中与查询相关的比例。

### Context Recall (上下文召回率)
衡量检索到的上下文中包含所有相关上下文的比例。

## 📁 项目结构

```
RAGAS/
├── official_ragas_evaluation.py    # 主评测程序
├── test_ragas_evaluation.py        # 测试脚本
├── requirements.txt                # 依赖包列表
└── README.md                      # 说明文档
```

## ⚙️ 配置说明

### API密钥配置
在 `backend/.env` 文件中配置SiliconFlow API密钥：

```env
SILICONFLOW_API_KEY=sk-xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx
```

### 模型配置
默认使用以下模型：
- LLM: Qwen/Qwen2-7B-Instruct
- Embedding: BAAI/bge-m3

## 🧪 测试系统

运行测试脚本验证系统配置：

```bash
python test_ragas_evaluation.py
```

## 📈 评测报告

评测完成后会生成JSON格式的报告文件，包含：
- 详细的评测结果
- 各项指标的统计信息
- 性能分布情况
- 优化建议

## 🛠️ 故障排除

### API密钥错误
确保在 `backend/.env` 文件中正确配置了SiliconFlow API密钥。

### 依赖包缺失
运行以下命令安装所有依赖：

```bash
pip install -r requirements.txt
```

### 模型连接问题
检查网络连接和API密钥是否正确。

## 📞 技术支持

如遇到问题，请联系项目维护人员或查看RAGAS官方文档：https://docs.ragas.io/
