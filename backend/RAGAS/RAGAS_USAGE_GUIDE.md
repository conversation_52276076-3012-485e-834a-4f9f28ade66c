# RAGAS使用完全指南

## 🎯 如何正确使用RAGAS评估

### 1. 基本使用模式

#### 模式一：直接评估（推荐）
```python
from datasets import Dataset
from ragas import evaluate
from ragas.metrics import Faithfulness, AnswerRelevancy

# 准备数据
data = {
    "question": ["患者头痛应该做什么检查？"],
    "answer": ["建议头部CT检查"],
    "contexts": [["头痛患者首选CT检查"]],
    "ground_truth": ["头部CT"]
}
dataset = Dataset.from_dict(data)

# 执行评估
result = evaluate(dataset, metrics=[Faithfulness(), AnswerRelevancy()])
```

#### 模式二：集成到现有流程
```python
# 在trace_five_cases.py中启用RAGAS
payload = {
    "clinical_query": query,
    "compute_ragas": True,
    "ground_truth": ground_truth
}
```

### 2. 数据格式要求

#### 必需字段
- `question`: 患者临床问题
- `answer`: LLM生成的推荐答案
- `contexts`: 检索到的上下文（必须是字符串列表）
- `ground_truth`: 标准答案

#### 数据验证
```python
def validate_data(item):
    return all([
        item.get("question"),
        item.get("answer"), 
        isinstance(item.get("contexts"), list),
        item.get("ground_truth")
    ])
```

### 3. 指标解释

| 指标 | 含义 | 理想值 |
|------|------|--------|
| Faithfulness | 答案是否忠实于上下文 | >0.8 |
| Answer Relevancy | 答案与问题的相关性 | >0.7 |
| Context Precision | 上下文精确度 | >0.8 |
| Context Recall | 上下文召回率 | >0.8 |

### 4. 常见问题解决

#### NaN值问题
- 确保数据完整
- 检查上下文格式
- 验证API连接

#### IndexError
- 检查数据长度
- 验证列表索引
- 添加边界检查

### 5. 实际使用示例

#### 运行trace_five_cases.py
```bash
cd /Users/<USER>/git_project_vscode/09_medical/ACRAC-web/backend/RAGAS
python trace_five_cases.py
```

#### 运行演示
```bash
python comprehensive_ragas_demo.py
```

### 6. 最佳实践

1. **数据准备**
   - 确保ground_truth准确
   - 上下文与问题相关
   - 答案简洁明了

2. **评估策略**
   - 先小批量测试
   - 逐步扩大规模
   - 监控异常值

3. **结果分析**
   - 关注低分样本
   - 分析失败原因
   - 持续改进

### 7. 调试技巧

#### 日志查看
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

#### 结果检查
```python
result_df = result.to_pandas()
print(result_df.describe())
```

### 8. 集成建议

#### 与现有API集成
在`trace_five_cases.py`中，RAGAS已经集成到API响应中：

```python
# API会自动计算RAGAS分数
response = requests.post(API, json={
    "compute_ragas": True,
    "ground_truth": ground_truth
})

# 从响应中获取RAGAS分数
ragas_scores = response.json().get('trace', {}).get('ragas_scores')
```

### 9. 性能优化

#### 批量处理
```python
# 批量评估多个样本
batch_size = 10
for i in range(0, len(data), batch_size):
    batch = data[i:i+batch_size]
    # 处理批次
```

#### 缓存结果
```python
# 缓存已评估的结果
cache_file = "ragas_cache.json"
if os.path.exists(cache_file):
    with open(cache_file) as f:
        cached = json.load(f)
```

## 🎉 总结

RAGAS现在可以稳定运行，支持：
- ✅ 正确评估医疗推荐质量
- ✅ 集成到现有API流程
- ✅ 批量处理和结果分析
- ✅ 完整的错误处理

**推荐使用**：`comprehensive_ragas_demo.py` 作为起点，逐步扩展到完整数据集。