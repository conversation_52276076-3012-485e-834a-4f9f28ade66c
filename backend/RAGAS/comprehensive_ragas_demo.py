#!/usr/bin/env python3
"""
完整的RAGAS使用示例 - 如何正确集成到医疗评估流程
展示从数据准备到结果分析的完整流程
"""

import os
import json
import pandas as pd
import numpy as np
from pathlib import Path
import sys
from typing import Dict, List, Any
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 添加路径
sys.path.insert(0, str(Path(__file__).parent.parent))

try:
    from datasets import Dataset
    from ragas import evaluate
    from ragas.metrics import (
        Faithfulness,
        AnswerRelevancy,
        ContextPrecision,
        ContextRecall,
    )
    from ragas.llms import LangchainLLMWrapper
    from ragas.embeddings import LangchainEmbeddingsWrapper
    from langchain_openai import ChatOpenAI, OpenAIEmbeddings
    RAGAS_AVAILABLE = True
except ImportError as e:
    logger.error(f"RAGAS导入失败: {e}")
    RAGAS_AVAILABLE = False

class ComprehensiveRAGASDemo:
    """完整的RAGAS演示类"""
    
    def __init__(self):
        self.setup_models()
        
    def setup_models(self):
        """设置RAGAS模型"""
        try:
            from dotenv import load_dotenv
            load_dotenv(Path(__file__).parent.parent / ".env")
            
            self.llm = ChatOpenAI(
                model="deepseek-ai/DeepSeek-R1",
                openai_api_base="https://api.siliconflow.cn/v1",
                openai_api_key=os.getenv("SILICONFLOW_API_KEY"),
                temperature=0.1,
                max_tokens=1000
            )
            
            self.embeddings = OpenAIEmbeddings(
                model="BAAI/bge-large-zh-v1.5",
                openai_api_base="https://api.siliconflow.cn/v1",
                openai_api_key=os.getenv("SILICONFLOW_API_KEY")
            )
            
            self.evaluator_llm = LangchainLLMWrapper(self.llm)
            self.evaluator_embeddings = LangchainEmbeddingsWrapper(self.embeddings)
            
        except Exception as e:
            logger.error(f"模型设置失败: {e}")
            raise
    
    def load_test_data(self, file_path: str) -> List[Dict[str, Any]]:
        """加载测试数据"""
        try:
            df = pd.read_excel(file_path)
            
            test_data = []
            for _, row in df.head(5).iterrows():  # 使用前5个样本
                test_data.append({
                    "question": str(row.get('临床场景', '')),
                    "answer": str(row.get('LLM答案', '')),  # 需要预先运行获得
                    "contexts": [str(row.get('检索到的上下文', ''))],  # 需要预先收集
                    "ground_truth": str(row.get('首选检查项目（标准化）', '')).strip('* ')
                })
            
            return test_data
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            return []
    
    def create_sample_data(self) -> List[Dict[str, Any]]:
        """创建示例数据用于演示"""
        return [
            {
                "question": "35岁女性，头痛3天伴恶心呕吐，推荐什么影像学检查？",
                "answer": "推荐头部CT平扫作为首选检查，可以快速排除颅内出血等急性病变。",
                "contexts": [
                    "对于急性头痛患者，头部CT是首选的影像学检查方法。",
                    "CT可以快速识别颅内出血、占位性病变等急性病变。"
                ],
                "ground_truth": "头部CT平扫"
            },
            {
                "question": "60岁男性，胸痛2小时，心电图ST段抬高，需要什么检查？",
                "answer": "需要紧急行冠状动脉造影检查，明确冠脉病变程度。",
                "contexts": [
                    "ST段抬高型心肌梗死需要紧急冠脉造影。",
                    "冠脉造影是诊断冠心病的金标准。"
                ],
                "ground_truth": "冠状动脉造影"
            },
            {
                "question": "5岁儿童，发热咳嗽3天，肺部听诊湿啰音，推荐什么检查？",
                "answer": "建议胸部X线检查，评估肺部感染情况。",
                "contexts": [
                    "儿童肺炎首选胸部X线检查。",
                    "X线可以显示肺部浸润、实变等病变。"
                ],
                "ground_truth": "胸部X线检查"
            }
        ]
    
    def prepare_ragas_dataset(self, data: List[Dict[str, Any]]) -> Dataset:
        """准备RAGAS数据集"""
        questions = []
        answers = []
        contexts = []
        ground_truths = []
        
        for item in data:
            # 确保数据完整性
            question = item.get("question", "").strip()
            answer = item.get("answer", "").strip()
            context_list = item.get("contexts", [])
            ground_truth = item.get("ground_truth", "").strip()
            
            if not all([question, answer, ground_truth]):
                logger.warning(f"跳过不完整数据: {item}")
                continue
            
            # 确保上下文是字符串列表
            if isinstance(context_list, str):
                context_list = [context_list]
            
            questions.append(question)
            answers.append(answer)
            contexts.append(context_list)
            ground_truths.append(ground_truth)
        
        return Dataset.from_dict({
            "question": questions,
            "answer": answers,
            "contexts": contexts,
            "ground_truth": ground_truths
        })
    
    def evaluate_batch(self, dataset: Dataset) -> Dict[str, Any]:
        """批量评估"""
        try:
            metrics = [
                Faithfulness(llm=self.evaluator_llm),
                AnswerRelevancy(llm=self.evaluator_llm, embeddings=self.evaluator_embeddings),
                ContextPrecision(llm=self.evaluator_llm),
                ContextRecall(llm=self.evaluator_llm)
            ]
            
            result = evaluate(
                dataset=dataset,
                metrics=metrics,
                llm=self.evaluator_llm,
                embeddings=self.evaluator_embeddings
            )
            
            return result
            
        except Exception as e:
            logger.error(f"批量评估失败: {e}")
            raise
    
    def analyze_results(self, result) -> Dict[str, Any]:
        """分析评估结果"""
        df = result.to_pandas()
        
        analysis = {
            "total_samples": len(df),
            "metrics": {}
        }
        
        for metric in ["faithfulness", "answer_relevancy", "context_precision", "context_recall"]:
            if metric in df.columns:
                values = df[metric].dropna()
                analysis["metrics"][metric] = {
                    "mean": float(values.mean()),
                    "std": float(values.std()),
                    "min": float(values.min()),
                    "max": float(values.max()),
                    "count": len(values)
                }
        
        return analysis
    
    def run_demo(self):
        """运行完整演示"""
        logger.info("🚀 开始RAGAS完整演示...")
        
        # 1. 准备数据
        test_data = self.create_sample_data()
        logger.info(f"✅ 准备了 {len(test_data)} 个测试样本")
        
        # 2. 准备数据集
        dataset = self.prepare_ragas_dataset(test_data)
        logger.info(f"✅ 数据集准备完成，共 {len(dataset)} 条记录")
        
        # 3. 执行评估
        result = self.evaluate_batch(dataset)
        logger.info("✅ 评估完成")
        
        # 4. 分析结果
        analysis = self.analyze_results(result)
        
        # 5. 保存结果
        timestamp = pd.Timestamp.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"comprehensive_ragas_demo_{timestamp}.json"
        
        final_result = {
            "analysis": analysis,
            "raw_data": test_data,
            "timestamp": pd.Timestamp.now().isoformat()
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(final_result, f, ensure_ascii=False, indent=2)
        
        # 6. 打印结果
        print("\n" + "="*60)
        print("🎯 RAGAS评估结果")
        print("="*60)
        
        for metric, stats in analysis["metrics"].items():
            print(f"{metric}: {stats['mean']:.3f} (±{stats['std']:.3f})")
        
        print(f"\n评估样本数: {analysis['total_samples']}")
        print(f"结果已保存: {output_file}")
        print("="*60)
        
        return final_result

def main():
    """主函数"""
    if not RAGAS_AVAILABLE:
        print("❌ RAGAS不可用，请先安装依赖")
        return
    
    try:
        demo = ComprehensiveRAGASDemo()
        result = demo.run_demo()
        return result
    except Exception as e:
        logger.error(f"演示失败: {e}")
        raise

if __name__ == "__main__":
    main()