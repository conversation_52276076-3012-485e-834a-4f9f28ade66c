# ACRAC组件设计规范指南

## 1. 组件设计原则

### 1.1 一致性原则
所有组件应遵循统一的设计语言和交互模式，确保用户在不同页面间切换时体验一致。

### 1.2 可复用性原则
组件设计应考虑通用性，能够在不同场景下复用，减少重复开发。

### 1.3 可访问性原则
所有组件必须满足WCAG 2.1 AA级标准，支持键盘导航和屏幕阅读器。

### 1.4 性能优化原则
组件应尽量减少DOM节点数量，避免不必要的重渲染，优化加载性能。

## 2. 组件分类与结构

### 2.1 基础组件
基础组件是构建复杂组件的基本元素，包括：
- 按钮(Button)
- 输入框(Input)
- 下拉选择(Select)
- 表格(Table)
- 卡片(Card)
- 图标(Icon)

### 2.2 业务组件
业务组件封装了特定业务逻辑，包括：
- 智能搜索(SmartSearch)
- 统计卡片(EnhancedStatsCard)
- 数据表格(DataTable)
- 表单组件(Form)
- 图表组件(Chart)

### 2.3 布局组件
布局组件用于页面结构组织，包括：
- 页面容器(PageContainer)
- 导航栏(Navigation)
- 侧边栏(Sidebar)
- 面包屑(Breadcrumb)
- 页脚(Footer)

## 3. 组件API设计规范

### 3.1 Props设计
1. **命名规范**：使用camelCase命名法
2. **类型定义**：所有props必须有明确的类型定义
3. **默认值**：为可选props提供合理的默认值
4. **验证机制**：使用Vue的prop验证机制

```typescript
interface Props {
  // 基本类型
  title: string
  count: number
  isActive: boolean
  
  // 对象类型
  config: {
    size: 'small' | 'medium' | 'large'
    theme: 'light' | 'dark'
  }
  
  // 数组类型
  items: string[]
  
  // 可选属性
  description?: string
  
  // 事件处理函数
  onClick?: (event: MouseEvent) => void
}
```

### 3.2 Events设计
1. **命名规范**：使用kebab-case命名法
2. **语义明确**：事件名称应清晰表达触发条件
3. **参数规范**：提供必要的事件参数

```typescript
interface Emits {
  // 基本事件
  change: [value: string]
  
  // 带参数的事件
  select: [item: Item, index: number]
  
  // 自定义事件
  'update:modelValue': [value: string]
}
```

### 3.3 Slots设计
1. **默认插槽**：提供基本内容插入能力
2. **具名插槽**：为复杂组件提供多个插入点
3. **作用域插槽**：传递数据给插槽内容

```vue
<template>
  <div class="custom-component">
    <!-- 默认插槽 -->
    <slot />
    
    <!-- 具名插槽 -->
    <slot name="header" />
    
    <!-- 作用域插槽 -->
    <slot name="item" :item="currentItem" :index="currentIndex" />
  </div>
</template>
```

## 4. 核心组件详细规范

### 4.1 SmartSearch 智能搜索组件

#### 4.1.1 Props
```typescript
interface SmartSearchProps {
  modelValue: string
  placeholder?: string
  suggestions?: SearchSuggestion[]
  history?: string[]
  loading?: boolean
  disabled?: boolean
  clearable?: boolean
  size?: 'small' | 'medium' | 'large'
}
```

#### 4.1.2 Events
```typescript
interface SmartSearchEmits {
  'update:modelValue': [value: string]
  search: [query: string, filters?: any]
  select: [item: SearchSuggestion]
  'advanced-search': [form: AdvancedSearchForm]
  clear: []
}
```

#### 4.1.3 Slots
- default: 自定义搜索框内容
- prefix: 搜索框前缀内容
- suffix: 搜索框后缀内容

#### 4.1.4 方法
```typescript
interface SmartSearchMethods {
  focus: () => void
  blur: () => void
  clear: () => void
  addHistory: (query: string) => void
}
```

### 4.2 EnhancedStatsCard 统计卡片组件

#### 4.2.1 Props
```typescript
interface EnhancedStatsCardProps {
  icon: Component
  value: number | string
  label: string
  trend?: {
    type: 'up' | 'down' | 'stable'
    value: string
    text?: string
  }
  subData?: {
    label: string
    value: string
    color?: string
  }
  chartData?: number[]
  showAction?: boolean
  color?: 'primary' | 'success' | 'warning' | 'error' | 'info'
}
```

#### 4.2.2 Events
```typescript
interface EnhancedStatsCardEmits {
  action: []
}
```

#### 4.2.3 Slots
- default: 自定义卡片内容
- icon: 自定义图标区域
- action: 自定义操作区域

### 4.3 HoverEffects 悬停效果组件

#### 4.3.1 Props
```typescript
interface HoverEffectsProps {
  effect?: 'lift' | 'scale' | 'glow' | 'ripple' | 'float' | 'tilt' | 'bounce'
  intensity?: 'subtle' | 'medium' | 'strong'
  duration?: number
  disabled?: boolean
  showIndicator?: boolean
  color?: string
}
```

#### 4.3.2 Events
```typescript
interface HoverEffectsEmits {
  hover: [isHovered: boolean]
  click: [event: MouseEvent]
}
```

#### 4.3.3 Slots
- default: 应用悬停效果的内容

### 4.4 ListTransition 列表过渡组件

#### 4.4.1 Props
```typescript
interface ListTransitionProps {
  animationType?: 'fade' | 'slide' | 'stagger'
  duration?: number
  staggerDelay?: number
  appear?: boolean
}
```

#### 4.4.2 Slots
- default: 需要应用过渡效果的列表元素

## 5. 样式规范

### 5.1 CSS类名命名规范
1. 使用BEM命名法：block__element--modifier
2. 组件根元素使用组件名作为类名
3. 子元素使用双下划线连接
4. 状态修饰符使用双横线连接

```scss
// 正确示例
.stats-card {
  // 组件根元素
  &__header {
    // 子元素
  }
  
  &__content {
    // 子元素
  }
  
  &--primary {
    // 状态修饰符
  }
  
  &--loading {
    // 状态修饰符
  }
}
```

### 5.2 CSS变量使用
1. 使用设计系统定义的CSS变量
2. 避免硬编码颜色和尺寸值
3. 为组件提供可定制的CSS变量

```scss
.enhanced-stats-card {
  // 使用CSS变量
  background: var(--acrac-bg-color);
  border-radius: var(--acrac-border-radius-base);
  box-shadow: var(--acrac-box-shadow-light);
  
  // 组件特定变量
  --stats-card-padding: var(--acrac-spacing-lg);
  --stats-card-header-gap: var(--acrac-spacing-md);
}
```

### 5.3 响应式设计
1. 使用设计系统的断点变量
2. 优先使用移动优先的媒体查询
3. 为不同屏幕尺寸提供适配样式

```scss
.responsive-component {
  // 默认移动端样式
  padding: var(--acrac-spacing-md);
  
  // 平板端适配
  @media (min-width: $breakpoint-md) {
    padding: var(--acrac-spacing-lg);
  }
  
  // 桌面端适配
  @media (min-width: $breakpoint-lg) {
    padding: var(--acrac-spacing-xl);
  }
}
```

## 6. 可访问性规范

### 6.1 语义化HTML
1. 使用正确的HTML标签表达内容语义
2. 为表单元素提供label标签
3. 使用heading标签建立内容层次

```vue
<template>
  <form @submit.prevent="handleSubmit">
    <label for="search-input">搜索关键词</label>
    <input 
      id="search-input"
      v-model="searchQuery"
      type="text"
      aria-describedby="search-help"
    />
    <p id="search-help">请输入您要搜索的关键词</p>
    <button type="submit">搜索</button>
  </form>
</template>
```

### 6.2 ARIA属性
1. 为复杂组件添加适当的ARIA属性
2. 使用aria-label为图标按钮提供说明
3. 使用aria-live通知动态内容变化

```vue
<template>
  <div 
    role="alert"
    aria-live="polite"
    :aria-hidden="!showMessage"
  >
    {{ message }}
  </div>
  
  <button 
    :aria-label="isExpanded ? '收起详情' : '展开详情'"
    @click="toggleExpand"
  >
    <svg :aria-hidden="true">
      <!-- 图标内容 -->
    </svg>
  </button>
</template>
```

### 6.3 键盘导航
1. 确保所有交互元素可通过键盘访问
2. 为自定义组件实现键盘事件处理
3. 提供键盘快捷键支持

```vue
<template>
  <div 
    tabindex="0"
    @keydown.enter="handleEnter"
    @keydown.space="handleSpace"
    @keydown.esc="handleEscape"
  >
    <!-- 组件内容 -->
  </div>
</template>
```

## 7. 性能优化规范

### 7.1 组件懒加载
1. 使用动态import实现组件懒加载
2. 为大型组件提供异步加载方案

```typescript
// 路由级懒加载
const AsyncComponent = () => import('@/components/AsyncComponent.vue')

// 条件懒加载
const LazyComponent = defineAsyncComponent(() => import('@/components/LazyComponent.vue'))
```

### 7.2 事件监听优化
1. 在组件销毁时移除事件监听器
2. 使用事件委托减少监听器数量
3. 避免在渲染函数中绑定内联事件处理器

```typescript
export default {
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  
  beforeUnmount() {
    window.removeEventListener('resize', this.handleResize)
  },
  
  methods: {
    handleResize: debounce(function() {
      // 处理resize逻辑
    }, 300)
  }
}
```

### 7.3 计算属性优化
1. 使用computed缓存复杂计算结果
2. 避免在计算属性中执行副作用操作
3. 为计算属性提供合理的依赖追踪

```typescript
export default {
  computed: {
    // 正确：纯函数计算，有缓存
    filteredItems() {
      return this.items.filter(item => item.active)
    },
    
    // 错误：包含副作用
    badExample() {
      console.log('This will run on every access')
      return this.items.filter(item => item.active)
    }
  }
}
```

## 8. 测试规范

### 8.1 单元测试
1. 为每个组件编写单元测试
2. 测试组件的props、events和slots
3. 验证组件在不同状态下的行为

```typescript
// 组件测试示例
describe('EnhancedStatsCard', () => {
  it('should render correctly with required props', () => {
    const wrapper = mount(EnhancedStatsCard, {
      props: {
        icon: 'Folder',
        value: 100,
        label: '测试标签'
      }
    })
    
    expect(wrapper.find('.stats-value').text()).toBe('100')
    expect(wrapper.find('.stats-label').text()).toBe('测试标签')
  })
  
  it('should emit action event when clicked', async () => {
    const wrapper = mount(EnhancedStatsCard, {
      props: {
        icon: 'Folder',
        value: 100,
        label: '测试标签',
        showAction: true
      }
    })
    
    await wrapper.find('.action-btn').trigger('click')
    expect(wrapper.emitted('action')).toBeTruthy()
  })
})
```

### 8.2 集成测试
1. 测试组件间的交互
2. 验证组件在实际使用场景中的表现
3. 测试路由和状态管理集成

### 8.3 端到端测试
1. 测试完整的用户操作流程
2. 验证跨页面的功能集成
3. 测试不同浏览器的兼容性

## 9. 文档规范

### 9.1 组件文档
每个组件应包含以下文档内容：
1. **组件说明**：简要描述组件用途
2. **Props说明**：详细说明每个prop的类型、默认值和用途
3. **Events说明**：详细说明每个事件的触发条件和参数
4. **Slots说明**：详细说明每个插槽的用途和作用域
5. **使用示例**：提供典型的使用示例

### 9.2 代码注释
1. 为复杂逻辑添加行内注释
2. 为函数和方法添加JSDoc注释
3. 为组件提供整体说明注释

```typescript
/**
 * 智能搜索组件
 * 提供关键词搜索、建议列表、搜索历史等功能
 * 
 * @example
 * <SmartSearch
 *   v-model="searchQuery"
 *   :suggestions="suggestions"
 *   :history="searchHistory"
 *   @search="handleSearch"
 * />
 */
export default defineComponent({
  name: 'SmartSearch',
  
  /**
   * 处理搜索事件
   * @param query 搜索关键词
   * @param filters 搜索过滤条件
   */
  methods: {
    handleSearch(query: string, filters?: any) {
      // 搜索逻辑实现
    }
  }
})
```