#!/usr/bin/env python3
"""
分析Excel文件中各字段的实际长度
"""

import pandas as pd
import json

def analyze_excel_field_lengths():
    """分析Excel文件中字段的实际长度"""
    
    excel_path = "/Users/<USER>/git_project_vscode/09_medical/ACRAC-web/影像测试样例-0318-1.xlsx"
    
    print("=== Excel文件字段长度分析 ===")
    print()
    
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        
        print(f"1. 文件基本信息:")
        print(f"   文件路径: {excel_path}")
        print(f"   总行数: {len(df)}")
        print(f"   列名: {list(df.columns)}")
        print()
        
        # 分析各列的字段长度
        print(f"2. 各字段长度统计:")
        for col in df.columns:
            if df[col].dtype == 'object':  # 文本字段
                lengths = df[col].astype(str).str.len()
                print(f"   {col}:")
                print(f"     最小长度: {lengths.min()}")
                print(f"     最大长度: {lengths.max()}")
                print(f"     平均长度: {lengths.mean():.1f}")
                print(f"     中位数长度: {lengths.median():.1f}")
                
                # 显示最长的几个值
                max_length_idx = lengths.idxmax()
                max_value = df.loc[max_length_idx, col]
                print(f"     最长内容: '{max_value}' (长度: {len(str(max_value))})")
                print()
        
        print(f"3. 详细数据样本分析:")
        for i, row in df.head(10).iterrows():
            print(f"   第{i+1}行:")
            for col in df.columns:
                value = str(row[col])
                print(f"     {col}: '{value}' (长度: {len(value)})")
            print()
        
        print(f"4. ClinicalScenarioData表字段映射分析:")
        print(f"   数据库字段 -> Excel字段 -> 当前VARCHAR长度 -> 实际需要长度")
        
        # 分析question_id字段 (对应题号)
        if '题号' in df.columns:
            question_ids = df['题号'].astype(str)
            max_qid_len = question_ids.str.len().max()
            print(f"   question_id -> 题号 -> VARCHAR(50) -> 实际最大{max_qid_len}字符 ✅")
        
        # 分析clinical_query字段 (对应临床场景)
        if '临床场景' in df.columns:
            clinical_queries = df['临床场景'].astype(str)
            max_cq_len = clinical_queries.str.len().max()
            print(f"   clinical_query -> 临床场景 -> TEXT -> 实际最大{max_cq_len}字符 ✅")
        
        # 分析ground_truth字段 (对应首选检查项目)
        if '首选检查项目（标准化）' in df.columns:
            ground_truths = df['首选检查项目（标准化）'].astype(str)
            max_gt_len = ground_truths.str.len().max()
            print(f"   ground_truth -> 首选检查项目 -> TEXT -> 实际最大{max_gt_len}字符 ✅")
        
        # upload_batch_id是系统生成的，不来自Excel
        print(f"   upload_batch_id -> 系统生成 -> VARCHAR(50->100) -> 实际需要53字符 ⚠️")
        
        print()
        print(f"5. 结论:")
        print(f"   ✅ Excel原始数据字段都很短，不需要100字符长度")
        print(f"   ❌ upload_batch_id是系统生成的UUID+时间戳，长度53字符")
        print(f"   ✅ 将upload_batch_id改为VARCHAR(100)是正确的解决方案")
        print(f"   📝 问题不在于原始文档字段过长，而在于系统设计的批次ID过长")
        
        return {
            'total_rows': len(df),
            'columns': list(df.columns),
            'field_lengths': {
                col: {
                    'max': df[col].astype(str).str.len().max(),
                    'min': df[col].astype(str).str.len().min(),
                    'avg': df[col].astype(str).str.len().mean()
                } for col in df.columns
            }
        }
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return None

if __name__ == "__main__":
    result = analyze_excel_field_lengths()
    
    if result:
        print("\n=== 数据库字段设计建议 ===")
        print("基于实际数据分析，建议的字段长度:")
        print("- question_id: VARCHAR(20) (当前50，可以缩短)")
        print("- clinical_query: TEXT (当前TEXT，合适)")
        print("- ground_truth: TEXT (当前TEXT，合适)")
        print("- upload_batch_id: VARCHAR(100) (当前已修改，必需)")
        print("- source_file: VARCHAR(255) (文件名长度)")