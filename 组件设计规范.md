# ACRAC系统组件设计规范

## 1. 组件设计原则

### 1.1 一致性原则
- 统一的视觉风格和交互模式
- 保持组件在不同场景下的一致性
- 遵循设计系统规范

### 1.2 可复用性原则
- 组件设计应具备通用性
- 支持灵活配置以适应不同场景
- 易于扩展和维护

### 1.3 可访问性原则
- 支持键盘导航
- 兼容屏幕阅读器
- 符合WCAG标准

### 1.4 性能原则
- 组件应具备良好的性能表现
- 支持按需加载
- 避免不必要的重渲染

## 2. 基础组件规范

### 2.1 按钮组件 (AButton)

#### 2.1.1 属性定义
| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| type | string | 'default' | 按钮类型：primary/success/warning/danger/info/default |
| size | string | 'medium' | 按钮大小：large/medium/small/mini |
| loading | boolean | false | 是否加载中状态 |
| disabled | boolean | false | 是否禁用 |
| icon | string | - | 图标类名 |
| plain | boolean | false | 是否朴素按钮 |
| round | boolean | false | 是否圆角按钮 |
| circle | boolean | false | 是否圆形按钮 |

#### 2.1.2 事件定义
| 事件名 | 参数 | 说明 |
|-------|------|------|
| click | (event: Event) | 点击事件 |
| focus | (event: Event) | 获得焦点事件 |
| blur | (event: Event) | 失去焦点事件 |

#### 2.1.3 插槽定义
| 插槽名 | 说明 |
|-------|------|
| default | 按钮内容 |
| icon | 图标内容 |

#### 2.1.4 样式规范
- 默认状态：背景色#FFFFFF，边框#DCDFE6，文字#606266
- 悬停状态：背景色#F5F7FA，边框#C0C4CC，文字#606266
- 激活状态：背景色#F0F2F5，边框#C0C4CC，文字#606266
- 禁用状态：背景色#FFFFFF，边框#EBEEF5，文字#C0C4CC

### 2.2 输入框组件 (AInput)

#### 2.2.1 属性定义
| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| type | string | 'text' | 输入框类型 |
| value/v-model | string/number | - | 绑定值 |
| placeholder | string | - | 占位文本 |
| clearable | boolean | false | 是否可清空 |
| disabled | boolean | false | 是否禁用 |
| readonly | boolean | false | 是否只读 |
| size | string | 'medium' | 输入框大小 |
| prefix-icon | string | - | 前置图标 |
| suffix-icon | string | - | 后置图标 |
| maxlength | number | - | 最大输入长度 |
| minlength | number | - | 最小输入长度 |

#### 2.2.2 事件定义
| 事件名 | 参数 | 说明 |
|-------|------|------|
| input | (value: string \| number) | 输入值变化时触发 |
| change | (value: string \| number) | 输入框失去焦点或按下回车时触发 |
| focus | (event: Event) | 获得焦点时触发 |
| blur | (event: Event) | 失去焦点时触发 |
| clear | - | 点击清空按钮时触发 |

#### 2.2.3 插槽定义
| 插槽名 | 说明 |
|-------|------|
| prefix | 输入框头部内容 |
| suffix | 输入框尾部内容 |
| prepend | 输入框前置内容 |
| append | 输入框后置内容 |

### 2.3 卡片组件 (ACard)

#### 2.3.1 属性定义
| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| header | string | - | 卡片标题 |
| body-style | object | - | 自定义body样式 |
| shadow | string | 'always' | 卡片阴影：always/hover/never |
| bordered | boolean | true | 是否有边框 |

#### 2.3.2 插槽定义
| 插槽名 | 说明 |
|-------|------|
| header | 卡片头部内容 |
| default | 卡片主体内容 |

## 3. 业务组件规范

### 3.1 统计卡片组件 (StatsCard)

#### 3.1.1 属性定义
| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| icon | Component | - | 图标组件 |
| value | number | 0 | 数值 |
| label | string | - | 标签文本 |
| trend | object | - | 趋势信息 |
| color | string | 'primary' | 颜色主题 |
| chart-data | number[] | - | 图表数据 |

#### 3.1.2 事件定义
| 事件名 | 参数 | 说明 |
|-------|------|------|
| action | - | 点击查看详情时触发 |

### 3.2 搜索组件 (SearchBox)

#### 3.2.1 属性定义
| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| modelValue/v-model | string | - | 搜索关键词 |
| placeholder | string | '请输入搜索关键词' | 占位文本 |
| suggestions | array | [] | 搜索建议 |
| loading | boolean | false | 是否加载中 |
| clearable | boolean | true | 是否可清空 |

#### 3.2.2 事件定义
| 事件名 | 参数 | 说明 |
|-------|------|------|
| update:modelValue | (value: string) | 输入值变化时触发 |
| search | (query: string) | 搜索时触发 |
| select | (item: object) | 选择建议项时触发 |
| focus | - | 获得焦点时触发 |
| blur | - | 失去焦点时触发 |

### 3.3 数据浏览组件 (DataBrowser)

#### 3.3.1 属性定义
| 属性名 | 类型 | 默认值 | 说明 |
|-------|------|--------|------|
| data | array | [] | 浏览数据 |
| level | string | 'panel' | 当前层级 |
| loading | boolean | false | 是否加载中 |
| selected | object | - | 当前选中项 |

#### 3.3.2 事件定义
| 事件名 | 参数 | 说明 |
|-------|------|------|
| select | (item: object) | 选择数据项时触发 |
| navigate | (level: string) | 导航层级时触发 |

## 4. 组件交互规范

### 4.1 悬停效果
- 所有可交互组件应具备悬停效果
- 悬停时应有视觉反馈（如颜色变化、阴影增强）
- 悬停动画时间：200ms

### 4.2 点击效果
- 点击时应有即时反馈（如颜色加深、缩放效果）
- 按钮点击动画时间：150ms
- 卡片点击提升效果：Y轴位移2px

### 4.3 加载状态
- 异步操作应显示加载状态
- 加载指示器应清晰可见
- 加载文字应简洁明确

### 4.4 错误状态
- 错误状态应有明确的视觉标识
- 提供错误信息和解决建议
- 支持重试操作

## 5. 组件可访问性规范

### 5.1 键盘导航
- 所有交互组件应支持Tab键导航
- 按钮组件应支持Enter和Space键激活
- 输入框组件应支持标准键盘操作

### 5.2 屏幕阅读器支持
- 为所有交互元素提供适当的ARIA标签
- 图标按钮应提供文字说明
- 状态变化应通知屏幕阅读器

### 5.3 焦点管理
- 焦点应清晰可见（默认焦点环宽度2px）
- 焦点顺序应符合视觉顺序
- 模态框打开时应锁定焦点

## 6. 组件性能优化

### 6.1 渲染优化
- 使用虚拟滚动处理大量数据
- 合理使用v-show和v-if
- 避免不必要的计算属性

### 6.2 事件优化
- 及时移除事件监听器
- 使用事件委托减少监听器数量
- 防抖和节流处理高频事件

### 6.3 内存优化
- 及时销毁组件实例
- 清理定时器和订阅
- 避免内存泄漏

## 7. 组件测试规范

### 7.1 单元测试
- 每个组件应有完整的单元测试
- 测试覆盖率应达到80%以上
- 包含正常流程和异常流程测试

### 7.2 集成测试
- 组件间交互应有集成测试
- 业务流程应有端到端测试
- 跨浏览器兼容性测试

### 7.3 可访问性测试
- 使用自动化工具检测可访问性问题
- 定期进行手动可访问性测试
- 符合WCAG 2.1 AA标准

## 8. 组件文档规范

### 8.1 文档结构
每个组件文档应包含以下部分：
1. 组件说明
2. 属性列表
3. 事件列表
4. 插槽列表
5. 使用示例
6. 注意事项

### 8.2 示例代码
- 提供完整的使用示例
- 包含常见使用场景
- 代码应可直接运行

### 8.3 最佳实践
- 提供组件使用最佳实践
- 列出常见问题和解决方案
- 性能优化建议