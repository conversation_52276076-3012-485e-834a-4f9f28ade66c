#!/usr/bin/env python3
"""
分析upload_batch_id字段长度问题
"""

import uuid
import time

def analyze_batch_id_length():
    """分析batch_id的实际长度"""
    
    print("=== ClinicalScenarioData表结构分析 ===")
    print()
    
    # 模拟生成upload_batch_id
    file_id = str(uuid.uuid4())  # UUID4格式: 8-4-4-4-12 = 36个字符
    timestamp = int(time.time())  # 当前时间戳，约10位数字
    upload_batch_id = f"batch_{file_id}_{timestamp}"
    
    print(f"1. upload_batch_id生成逻辑分析:")
    print(f"   格式: batch_{{file_id}}_{{timestamp}}")
    print(f"   file_id (UUID4): {file_id} (长度: {len(file_id)})")
    print(f"   timestamp: {timestamp} (长度: {len(str(timestamp))})")
    print(f"   完整batch_id: {upload_batch_id}")
    print(f"   实际长度: {len(upload_batch_id)} 字符")
    print()
    
    # 分析各部分长度
    prefix_len = len("batch_")
    uuid_len = len(file_id)
    separator_len = len("_")
    timestamp_len = len(str(timestamp))
    total_len = prefix_len + uuid_len + separator_len + timestamp_len
    
    print(f"2. 长度组成分析:")
    print(f"   'batch_' 前缀: {prefix_len} 字符")
    print(f"   UUID4: {uuid_len} 字符")
    print(f"   '_' 分隔符: {separator_len} 字符")
    print(f"   时间戳: {timestamp_len} 字符")
    print(f"   总长度: {total_len} 字符")
    print()
    
    print(f"3. 数据库字段长度需求分析:")
    if total_len <= 50:
        print(f"   ✅ 当前长度({total_len})在VARCHAR(50)范围内")
        print(f"   ❌ 但实际测试中出现了超长错误，可能存在其他因素")
    else:
        print(f"   ❌ 当前长度({total_len})超过VARCHAR(50)限制")
        print(f"   ✅ 需要VARCHAR(100)来容纳")
    
    print()
    print(f"4. 原始Excel数据分析:")
    print(f"   - 题号: 数字，长度很短")
    print(f"   - 临床场景: 文本描述，约20-50字符")
    print(f"   - 首选检查项目: 医学术语，约10-30字符")
    print(f"   ❌ 原始数据中没有长字段需要100字符")
    
    print()
    print(f"5. 问题根源分析:")
    print(f"   - upload_batch_id是系统生成的唯一标识符")
    print(f"   - 包含UUID(36字符) + 时间戳(10字符) + 前缀和分隔符(7字符)")
    print(f"   - 总长度约53字符，确实超过VARCHAR(50)")
    print(f"   - 这不是原始文档字段过长，而是系统设计问题")
    
    print()
    print(f"6. 解决方案建议:")
    print(f"   方案1: 将upload_batch_id字段改为VARCHAR(100) ✅ (已实施)")
    print(f"   方案2: 优化batch_id生成算法，使用更短的标识符")
    print(f"   方案3: 使用数据库自增ID作为batch_id")
    
    return {
        'batch_id': upload_batch_id,
        'length': len(upload_batch_id),
        'components': {
            'prefix': 'batch_',
            'uuid': file_id,
            'timestamp': str(timestamp)
        },
        'analysis': {
            'exceeds_50': len(upload_batch_id) > 50,
            'fits_100': len(upload_batch_id) <= 100
        }
    }

if __name__ == "__main__":
    result = analyze_batch_id_length()
    
    print("\n=== 测试多个batch_id生成 ===")
    for i in range(3):
        file_id = str(uuid.uuid4())
        timestamp = int(time.time()) + i
        batch_id = f"batch_{file_id}_{timestamp}"
        print(f"示例{i+1}: {batch_id} (长度: {len(batch_id)})")