# ACRAC简化前端设计方案

## 1. 设计目标

1. 使用 ant-design-vue 重构前端界面
2. 保持后端API不变
3. 实现最基本的功能，让业务先跑通
4. 简化代码结构，提高可维护性

## 2. 技术选型

- 框架：Vue 3 + TypeScript
- UI库：ant-design-vue
- 构建工具：Vite
- 路由：vue-router
- 状态管理：Pinia (按需使用)
- HTTP客户端：axios

## 3. 页面结构

### 3.1 核心页面
1. 首页 (Home) - 数据概览和快速导航
2. 搜索页 (Search) - 关键词搜索功能
3. 浏览页 (Browse) - 分层数据浏览
4. 推理页 (Inference) - AI推荐功能（占位）

### 3.2 页面功能要求
#### 首页
- 显示系统统计数据
- 提供快速导航入口

#### 搜索页
- 关键词搜索框
- 搜索结果展示
- 结果分类显示

#### 浏览页
- Panel → Topic → Variant → Procedure 四级浏览
- 面包屑导航
- 数据列表展示

## 4. 组件设计

### 4.1 基础组件
- AppHeader - 应用头部导航
- AppFooter - 应用底部信息
- DataCard - 数据展示卡片
- SearchBox - 搜索组件

### 4.2 业务组件
- StatsCard - 统计数据卡片
- BrowserPanel - 数据浏览面板
- SearchResult - 搜索结果组件

## 5. 实现步骤

### 5.1 环境准备
1. 安装 ant-design-vue
2. 配置按需加载
3. 移除原有UI库依赖

### 5.2 路由配置
1. 保持现有路由结构
2. 更新页面组件引用

### 5.3 页面重构
1. 重构首页组件
2. 重构搜索页组件
3. 重构浏览页组件
4. 保留推理页占位

### 5.4 样式调整
1. 移除Tailwind CSS配置
2. 使用Ant Design样式系统
3. 统一全局样式

## 6. 数据接口保持不变

所有后端API接口保持不变，前端仅调整UI实现：

- `/api/v1/data/import-stats` - 获取统计数据
- `/api/v1/panels/` - 获取专科列表
- `/api/v1/topics/?panel_id={id}` - 获取主题列表
- `/api/v1/variants/?topic_id={id}` - 获取情境列表
- `/api/v1/procedures/?variant_id={id}` - 获取检查项目列表
- `/api/v1/panels/?search={keyword}` - 搜索专科
- `/api/v1/topics/?search={keyword}` - 搜索主题
- `/api/v1/variants/?search={keyword}` - 搜索情境
- `/api/v1/procedures/?search={keyword}` - 搜索检查项目

## 7. 交付标准

1. 页面功能正常运行
2. UI风格统一
3. 响应式设计
4. 代码结构清晰
5. 无重大性能问题