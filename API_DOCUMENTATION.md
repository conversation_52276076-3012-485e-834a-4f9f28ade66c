# ACRAC API 使用文档

> 生成时间: 2025-09-08 20:32:35
> API版本: 1.0.0

## 概述

ACRAC (American College of Radiology Appropriateness Criteria) API 提供基于向量搜索的医疗影像智能推荐服务。

### 基础信息

- **基础URL**: `http://localhost:8000`
- **API版本**: v1
- **认证方式**: 无需认证
- **数据格式**: JSON

## 主要端点

### 1. 健康检查

#### GET /health
检查API服务状态

**响应示例:**
```json
{
  "status": "healthy",
  "service": "acrac-api"
}
```

#### GET /api/v1/acrac/vector/v2/health
检查向量搜索服务状态

**响应示例:**
```json
{
  "status": "healthy",
  "service": "vector_search_v2",
  "message": "向量搜索服务V2运行正常",
  "timestamp": **********.784102
}
```

### 2. 数据库统计

#### GET /api/v1/acrac/vector/v2/stats
获取数据库统计信息

**响应示例:**
```json
{
  "panels_count": 13,
  "panels_vector_coverage": 100.0,
  "topics_count": 285,
  "topics_vector_coverage": 100.0,
  "clinical_scenarios_count": 1391,
  "clinical_scenarios_vector_coverage": 100.0,
  "procedure_dictionary_count": 1053,
  "procedure_dictionary_vector_coverage": 100.0,
  "clinical_recommendations_count": 15970,
  "clinical_recommendations_vector_coverage": 100.0
}
```

### 3. 向量搜索

#### POST /api/v1/acrac/vector/v2/search/comprehensive
综合向量搜索 - 搜索所有实体类型

**请求参数:**
```json
{
  "query_text": "45岁女性，慢性反复头痛3年",
  "top_k": 10,
  "similarity_threshold": 0.0
}
```

**参数说明:**
- `query_text` (string, 必需): 搜索查询文本，1-1000字符
- `top_k` (integer, 可选): 返回结果数量，1-50，默认10
- `similarity_threshold` (float, 可选): 相似度阈值，0.0-1.0，默认0.0

**响应示例:**
```json
{
  "query_text": "45岁女性，慢性反复头痛3年",
  "search_time_ms": 960,
  "panels": [...],
  "topics": [...],
  "scenarios": [...],
  "procedures": [...],
  "recommendations": [...],
  "total_results": 43
}
```

#### POST /api/v1/acrac/vector/v2/search/scenarios
搜索临床场景

**请求参数:**
```json
{
  "query_text": "45岁女性，慢性反复头痛3年",
  "top_k": 5,
  "similarity_threshold": 0.0
}
```

**响应示例:**
```json
[
  {
    "id": 31,
    "semantic_id": "S0031",
    "description_zh": "成人≥40岁。病理性乳头溢液。初始影像学检查。",
    "description_en": "Variant 2: Adult male or female 40 years of age or older...",
    "patient_population": null,
    "risk_level": null,
    "age_group": null,
    "gender": null,
    "urgency_level": null,
    "symptom_category": null,
    "panel_name": "乳腺外科",
    "topic_name": "乳头溢液评估",
    "similarity_score": 0.024852960917039257
  }
]
```

#### POST /api/v1/acrac/vector/v2/search/recommendations
搜索临床推荐

**请求参数:**
```json
{
  "query_text": "45岁女性，慢性反复头痛3年",
  "top_k": 5,
  "similarity_threshold": 0.0
}
```

**响应示例:**
```json
[
  {
    "id": 9861,
    "semantic_id": "CR009861",
    "appropriateness_rating": 1,
    "appropriateness_category_zh": "通常不适宜",
    "reasoning_zh": "目前尚无相关文献支持在疑似或确诊大唾液腺癌的初始分期中常规使用无静...",
    "evidence_level": "Expert Consensus",
    "pregnancy_safety": "未评估",
    "adult_radiation_dose": "☢☢ 0.1-1mSv",
    "pediatric_radiation_dose": "",
    "scenario_description": "疑似或诊断大唾液腺（腮腺、颌下腺和舌下腺）癌，初始分期。",
    "patient_population": null,
    "risk_level": null,
    "procedure_name": "CT颌面部(平扫)",
    "modality": "CT",
    "body_part": "其他",
    "panel_name": "神经内科",
    "topic_name": "头颈部癌分期与治疗后评估",
    "similarity_score": 0.037762901247684
  }
]
```

#### POST /api/v1/acrac/vector/v2/search/panels
搜索科室

#### POST /api/v1/acrac/vector/v2/search/topics
搜索主题

#### POST /api/v1/acrac/vector/v2/search/procedures
搜索检查项目

## 使用示例

### Python示例

```python
import requests

# 基础URL
base_url = "http://localhost:8000"

# 搜索临床场景
def search_scenarios(query_text, top_k=5):
    response = requests.post(
        f"{base_url}/api/v1/acrac/vector/v2/search/scenarios",
        json={
            "query_text": query_text,
            "top_k": top_k,
            "similarity_threshold": 0.0
        }
    )
    return response.json()

# 使用示例
results = search_scenarios("45岁女性，慢性反复头痛3年")
for result in results:
    print(f"相似度: {result['similarity_score']:.4f}")
    print(f"描述: {result['description_zh']}")
    print(f"科室: {result['panel_name']}")
    print("---")
```

### cURL示例

```bash
# 搜索临床场景
curl -X POST "http://localhost:8000/api/v1/acrac/vector/v2/search/scenarios" \
  -H "Content-Type: application/json" \
  -d '{"query_text": "45岁女性，慢性反复头痛3年", "top_k": 5}'

# 搜索临床推荐
curl -X POST "http://localhost:8000/api/v1/acrac/vector/v2/search/recommendations" \
  -H "Content-Type: application/json" \
  -d '{"query_text": "45岁女性，慢性反复头痛3年", "top_k": 5}'

# 获取数据库统计
curl -X GET "http://localhost:8000/api/v1/acrac/vector/v2/stats"
```

## 错误处理

### 常见错误码

- `422 Unprocessable Entity`: 请求参数验证失败
- `500 Internal Server Error`: 服务器内部错误

### 错误响应格式

```json
{
  "detail": "错误描述信息"
}
```

## 性能指标

- **平均响应时间**: ~1000ms
- **向量覆盖率**: 100%
- **数据规模**: 
  - 科室: 13个
  - 主题: 285个
  - 临床场景: 1,391个
  - 检查项目: 1,053个
  - 临床推荐: 15,970个

## 注意事项

1. **相似度阈值**: 建议设置为0.0-0.5之间，过低可能返回不相关结果
2. **查询长度**: 查询文本建议在10-200字符之间，过短或过长可能影响搜索效果
3. **结果数量**: top_k参数建议设置为5-20之间，过多可能影响响应时间
4. **中文支持**: API完全支持中文查询和响应

## 更新日志

- **v2.0.0** (2024-09-08): 重构API，支持新的数据库结构
- **v1.0.0** (2024-09-01): 初始版本发布

---

**技术支持**: ACRAC开发团队  
**文档版本**: v2.0.0  
**最后更新**: 2025-09-08
