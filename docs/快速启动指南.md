# ACRAC系统快速启动指南

## 立即开始

### 第一步：克隆项目结构

```bash
# 在 ACRAC-web 目录下创建项目结构
mkdir -p backend/{app,tests,scripts}
mkdir -p frontend/{src,public,tests}
mkdir -p docs
mkdir -p deployment/{docker,k8s}
mkdir -p data/{raw,processed}
```

### 第二步：后端环境初始化

```bash
# 进入后端目录
cd backend

# 创建虚拟环境
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 创建 requirements.txt
cat > requirements.txt << EOF
fastapi==0.104.1
uvicorn==0.24.0
sqlalchemy==2.0.23
psycopg2-binary==2.9.9
pandas==2.1.3
openpyxl==3.1.2
python-multipart==0.0.6
pydantic==2.5.0
alembic==1.12.1
redis==5.0.1
celery==5.3.4
pgvector==0.2.3
httpx==0.25.2
pytest==7.4.3
EOF

# 安装依赖
pip install -r requirements.txt
```

### 第三步：数据库设置

```bash
# 1. 安装 PostgreSQL 15 和 pgvector
# macOS
brew install postgresql@15
brew services start postgresql@15

# Ubuntu/Debian
sudo apt update
sudo apt install postgresql-15 postgresql-contrib-15

# 2. 创建数据库和安装 pgvector
psql -U postgres << EOF
CREATE DATABASE acrac_db;
\c acrac_db;
CREATE EXTENSION vector;
EOF

# 3. 设置环境变量
cat > .env << EOF
DATABASE_URL=*************************************************
REDIS_URL=redis://localhost:6379
SECRET_KEY=your-secret-key-here
EOF
```

### 第四步：创建基础代码结构

```bash
# backend/app/main.py
cat > app/main.py << 'EOF'
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

app = FastAPI(title="ACRAC API", version="1.0.0")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def read_root():
    return {"message": "ACRAC API is running"}

@app.get("/health")
def health_check():
    return {"status": "healthy"}
EOF

# backend/app/database.py
cat > app/database.py << 'EOF'
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

SQLALCHEMY_DATABASE_URL = os.getenv("DATABASE_URL", "*************************************************")

engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()
EOF

# backend/app/models.py
cat > app/models.py << 'EOF'
from sqlalchemy import Column, Integer, String, Text, Float, ForeignKey, TIMESTAMP
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from pgvector.sqlalchemy import Vector
from .database import Base

class Panel(Base):
    __tablename__ = "panels"
    
    id = Column(Integer, primary_key=True, index=True)
    name_en = Column(String(255))
    name_zh = Column(String(255))
    description = Column(Text)
    created_at = Column(TIMESTAMP, server_default=func.now())
    
    topics = relationship("Topic", back_populates="panel")

class Topic(Base):
    __tablename__ = "topics"
    
    id = Column(Integer, primary_key=True, index=True)
    panel_id = Column(Integer, ForeignKey("panels.id"))
    name_en = Column(String(255))
    name_zh = Column(String(255))
    description = Column(Text)
    
    panel = relationship("Panel", back_populates="topics")
    variants = relationship("Variant", back_populates="topic")

class Variant(Base):
    __tablename__ = "variants"
    
    id = Column(Integer, primary_key=True, index=True)
    topic_id = Column(Integer, ForeignKey("topics.id"))
    description_en = Column(Text)
    description_zh = Column(Text)
    clinical_context = Column(Text)
    
    topic = relationship("Topic", back_populates="variants")
    procedures = relationship("Procedure", back_populates="variant")

class Procedure(Base):
    __tablename__ = "procedures"
    
    id = Column(Integer, primary_key=True, index=True)
    variant_id = Column(Integer, ForeignKey("variants.id"))
    name_en = Column(String(255))
    name_zh = Column(String(255))
    recommendation_en = Column(Text)
    recommendation_zh = Column(Text)
    appropriateness_category = Column(String(50))
    rating = Column(Float)
    soe = Column(String(50))
    adult_rrl = Column(String(50))
    peds_rrl = Column(String(50))
    
    variant = relationship("Variant", back_populates="procedures")
EOF

# 启动后端服务
uvicorn app.main:app --reload
```

### 第五步：前端项目初始化

```bash
# 新开一个终端，进入项目根目录
cd frontend

# 使用 Vite 创建 Vue3 项目
npm create vite@latest . -- --template vue-ts

# 安装依赖
npm install

# 安装额外依赖
npm install element-plus @element-plus/icons-vue
npm install axios vue-router@4 pinia
npm install vue-i18n@9

# 启动开发服务器
npm run dev
```

### 第六步：Docker 配置

```bash
# docker-compose.yml
cat > ../docker-compose.yml << 'EOF'
version: '3.8'

services:
  postgres:
    image: pgvector/pgvector:pg15
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: acrac_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
  
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
  
  backend:
    build: ./backend
    volumes:
      - ./backend:/app
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
    environment:
      - DATABASE_URL=************************************************
      - REDIS_URL=redis://redis:6379
  
  frontend:
    build: ./frontend
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "5173:5173"

volumes:
  postgres_data:
EOF

# backend/Dockerfile
cat > ../backend/Dockerfile << 'EOF'
FROM python:3.10-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

COPY . .

CMD ["uvicorn", "app.main:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]
EOF

# frontend/Dockerfile
cat > ../frontend/Dockerfile << 'EOF'
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm install

COPY . .

EXPOSE 5173

CMD ["npm", "run", "dev", "--", "--host"]
EOF
```

## 第一阶段任务分配

### 开发人员A：环境搭建 + 数据模型
1. 完成 PostgreSQL + pgvector 安装配置
2. 设计并实现数据库表结构
3. 创建 Alembic 迁移脚本

### 开发人员B：数据导入功能
1. 实现 Excel/CSV 文件解析
2. 开发数据验证逻辑
3. 创建批量导入 API

### 开发人员C：基础查询 API
1. 实现四层数据查询接口
2. 添加分页和筛选功能
3. 编写 API 测试用例

### 开发人员D：前端基础界面
1. 搭建 Vue3 项目框架
2. 实现数据浏览页面
3. 开发层级导航组件

## 每日检查点

### 每日站会内容
1. 昨天完成了什么？
2. 今天计划做什么？
3. 遇到什么问题？
4. 需要什么支持？

### 每日代码提交规范
```bash
# 提交格式
git commit -m "feat: 添加Panel查询API"
git commit -m "fix: 修复分页bug"
git commit -m "docs: 更新API文档"

# 分支策略
main          # 主分支，稳定版本
develop       # 开发分支
feature/xxx   # 功能分支
hotfix/xxx    # 紧急修复
```

## 测试数据准备

```python
# scripts/import_test_data.py
import pandas as pd
from sqlalchemy.orm import Session
from app.database import SessionLocal, engine
from app.models import Base, Panel, Topic, Variant, Procedure

# 创建表
Base.metadata.create_all(bind=engine)

# 读取测试数据
df = pd.read_csv('../ACR_data/ACR_final.csv', nrows=100)

# 导入逻辑...
```

## 问题解决资源

### 技术文档
- FastAPI: https://fastapi.tiangolo.com/
- Vue3: https://vuejs.org/
- PostgreSQL: https://www.postgresql.org/docs/15/
- pgvector: https://github.com/pgvector/pgvector

### 社区支持
- 项目 Slack 频道：#acrac-dev
- 技术问题：创建 GitHub Issue
- 代码评审：Pull Request

### 紧急联系
- 技术负责人：<EMAIL>
- 项目经理：<EMAIL>
- DevOps：<EMAIL>

---
*立即开始，每一步都有明确的目标！*
