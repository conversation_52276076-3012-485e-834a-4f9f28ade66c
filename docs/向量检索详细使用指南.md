# 向量检索详细使用指南

## 概述

向量检索是ACRAC智能推荐系统的核心组件之一，通过语义相似度计算来找到最相关的医疗检查推荐。本文档详细说明向量检索的工作原理、使用方法和实现细节。

## 核心原理

### 1. 向量化表示
- **嵌入模型**: 使用BGE-M3 (bge-m3:latest) 生成1024维向量
- **向量存储**: 存储在`clinical_recommendations.embedding`字段中
- **向量类型**: JSON格式存储的浮点数数组

### 2. 相似度计算
- **算法**: 余弦相似度 (Cosine Similarity)
- **公式**: `cos(θ) = (A·B) / (||A|| × ||B||)`
- **范围**: [-1, 1]
- **解释**:
  - 1.0 = 完全相同
  - 0.0 = 正交（无关）
  - -1.0 = 完全相反
  - > 0.7 = 高度相似
  - 0.3-0.7 = 中等相似
  - < 0.3 = 低相似

## 工作流程

### 步骤1: 查询文本构建
```python
# 输入
patient_info = {
    "age": 45,
    "gender": "女性",
    "symptoms": ["慢性胸痛"],
    "duration": "3个月"
}
clinical_description = "45岁女性，慢性反复发作胸痛，无明显系统性异常体征"

# 构建查询文本
query_parts = [clinical_description]
if patient_info.get('age'):
    query_parts.append(f"{patient_info['age']}岁")
if patient_info.get('gender'):
    query_parts.append(patient_info['gender'])
if patient_info.get('symptoms'):
    query_parts.extend(patient_info['symptoms'])
if patient_info.get('duration'):
    query_parts.append(patient_info['duration'])

query_text = " ".join(query_parts)
# 结果: "45岁女性，慢性反复发作胸痛，无明显系统性异常体征 45岁 女性 慢性胸痛 3个月"
```

### 步骤2: 向量生成
```python
# 调用Ollama API生成向量
response = requests.post(
    "http://localhost:11434/api/embeddings",
    json={
        "model": "bge-m3:latest",
        "prompt": query_text
    },
    timeout=30
)

query_vector = response.json()['embedding']  # 1024维向量
```

### 步骤3: 数据库检索
```sql
SELECT 
    cr.semantic_id,
    cr.scenario_id,
    cr.procedure_id,
    cr.appropriateness_rating,
    cr.appropriateness_category_zh,
    cr.reasoning_zh,
    cr.evidence_level,
    cr.pregnancy_safety,
    cr.adult_radiation_dose,
    s.description_zh as scenario_desc,
    s.patient_population,
    s.risk_level,
    s.age_group,
    s.gender,
    s.pregnancy_status,
    pd.name_zh as procedure_name,
    pd.modality,
    pd.body_part,
    pd.contrast_used,
    pd.radiation_level,
    p.name_zh as panel_name,
    t.name_zh as topic_name,
    cr.embedding
FROM clinical_recommendations cr
JOIN clinical_scenarios s ON cr.scenario_id = s.semantic_id
JOIN procedure_dictionary pd ON cr.procedure_id = pd.semantic_id
JOIN topics t ON s.topic_id = t.id
JOIN panels p ON s.panel_id = p.id
WHERE cr.is_active = TRUE
  AND cr.embedding IS NOT NULL
LIMIT 20;
```

### 步骤4: 相似度计算
```python
def cosine_similarity(vec1, vec2):
    """计算余弦相似度"""
    vec1 = np.array(vec1)
    vec2 = np.array(vec2)
    
    # 确保向量长度一致
    min_len = min(len(vec1), len(vec2))
    vec1 = vec1[:min_len]
    vec2 = vec2[:min_len]
    
    # 计算余弦相似度
    dot_product = np.dot(vec1, vec2)
    norm1 = np.linalg.norm(vec1)
    norm2 = np.linalg.norm(vec2)
    
    if norm1 == 0 or norm2 == 0:
        return 0.0
    
    return dot_product / (norm1 * norm2)

# 对每个候选计算相似度
for row in result:
    candidate_embedding = json.loads(row[22])  # embedding字段
    similarity = cosine_similarity(query_vector, candidate_embedding)
    # 添加到候选列表...
```

### 步骤5: 结果排序和返回
```python
# 按相似度排序
candidates.sort(key=lambda x: x['similarity_score'], reverse=True)
candidates = candidates[:recall_size]  # 返回前N个
```

## API使用方法

### 端点
```
POST /api/v1/acrac/methods/vector-method
```

### 请求格式
```json
{
    "patient_description": "45岁女性，慢性反复发作胸痛，无明显系统性异常体征",
    "age": 45,
    "gender": "女性",
    "symptoms": ["慢性胸痛"],
    "max_recommendations": 5
}
```

### 响应格式
```json
{
    "recommendations": [
        {
            "procedure_name": "DR胸部正位",
            "modality": "XR",
            "body_part": "胸部",
            "appropriateness_rating": 8,
            "reasoning": "胸痛初始筛查",
            "evidence_level": "C",
            "radiation_level": "低",
            "panel_name": "胸部影像科"
        }
    ],
    "method": "向量检索法",
    "confidence": 0.85,
    "reasoning": "基于语义相似度的向量检索",
    "warnings": []
}
```

## 测试案例

### 案例1: 胸痛患者
```python
# 输入
{
    "patient_description": "45岁女性，慢性反复发作胸痛，无明显系统性异常体征",
    "age": 45,
    "gender": "女性",
    "symptoms": ["慢性胸痛"],
    "max_recommendations": 5
}

# 输出
[
    {
        "procedure_name": "DR胸部正位",
        "modality": "XR",
        "appropriateness_rating": 8,
        "reasoning": "胸痛初始筛查"
    },
    {
        "procedure_name": "CT冠状动脉CTA",
        "modality": "CT", 
        "appropriateness_rating": 8,
        "reasoning": "评估冠心病"
    }
]
```

### 案例2: 头痛患者
```python
# 输入
{
    "patient_description": "30岁男性，突发剧烈头痛，伴恶心呕吐",
    "age": 30,
    "gender": "男性",
    "symptoms": ["头痛", "恶心", "呕吐"],
    "max_recommendations": 5
}

# 输出
[
    {
        "procedure_name": "CT颅脑平扫",
        "modality": "CT",
        "appropriateness_rating": 9,
        "reasoning": "头痛初始评估"
    },
    {
        "procedure_name": "MR颅脑平扫+增强",
        "modality": "MRI",
        "appropriateness_rating": 8,
        "reasoning": "详细神经评估"
    }
]
```

## 性能特点

### 优势
- ✅ **语义理解**: 能理解查询的语义含义，不仅仅是关键词匹配
- ✅ **多语言支持**: BGE-M3模型支持中英文混合查询
- ✅ **相似性排序**: 按语义相似度自动排序，最相关的排在前面
- ✅ **上下文理解**: 能理解症状、年龄、性别等上下文信息
- ✅ **可扩展性**: 容易添加新的检查项目到向量库

### 限制
- ⚠️ **依赖向量质量**: 向量质量直接影响检索效果
- ⚠️ **计算开销**: 需要计算所有候选的相似度
- ⚠️ **模型依赖**: 依赖Ollama服务和嵌入模型
- ⚠️ **冷启动**: 首次查询需要生成向量，响应时间较长

## 技术实现细节

### 数据库字段
- `clinical_recommendations.embedding`: 存储1024维向量（JSON格式）
- `clinical_recommendations.is_active`: 是否激活的推荐
- 关联表: `clinical_scenarios`, `procedure_dictionary`, `topics`, `panels`

### 向量生成
- **模型**: BGE-M3 (bge-m3:latest)
- **维度**: 1024
- **API**: `http://localhost:11434/api/embeddings`
- **超时**: 30秒

### 相似度计算
- **算法**: 余弦相似度
- **实现**: NumPy
- **优化**: 向量长度对齐

## 故障排除

### 常见问题

1. **Ollama服务未启动**
   ```bash
   # 启动Ollama服务
   ollama serve
   
   # 拉取模型
   ollama pull bge-m3:latest
   ```

2. **向量生成失败**
   - 检查Ollama服务状态
   - 检查模型是否已下载
   - 检查网络连接

3. **数据库连接问题**
   - 检查数据库文件是否存在
   - 检查向量数据是否已生成
   - 检查数据库权限

4. **相似度计算异常**
   - 检查向量维度是否一致
   - 检查向量数据格式
   - 检查NumPy版本

### 调试方法

1. **启用详细日志**
   ```python
   import logging
   logging.basicConfig(level=logging.INFO)
   ```

2. **测试向量生成**
   ```python
   response = requests.post(
       "http://localhost:11434/api/embeddings",
       json={"model": "bge-m3:latest", "prompt": "测试文本"}
   )
   print(response.json())
   ```

3. **检查数据库数据**
   ```sql
   SELECT COUNT(*) FROM clinical_recommendations WHERE embedding IS NOT NULL;
   ```

## 与其他方法的对比

| 方法 | 优势 | 劣势 | 适用场景 |
|------|------|------|----------|
| **向量检索** | 语义理解、灵活 | 计算开销、模型依赖 | 快速语义搜索 |
| **规则匹配** | 精确、快速 | 缺乏语义理解 | 精确匹配场景 |
| **LLM分析** | 深度推理、智能 | 计算开销大、慢 | 复杂推理场景 |
| **RAG** | 最佳效果 | 最慢、最复杂 | 高质量推荐 |

## 总结

向量检索是ACRAC智能推荐系统的重要组成部分，通过语义相似度计算实现了智能的医疗检查推荐。它能够理解患者的症状描述、年龄、性别等上下文信息，并找到最相关的检查项目推荐。

虽然向量检索有一定的计算开销和模型依赖，但其语义理解能力和灵活性使其成为现代智能推荐系统的核心技术之一。
