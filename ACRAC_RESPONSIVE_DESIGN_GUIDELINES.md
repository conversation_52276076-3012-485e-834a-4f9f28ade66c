# ACRAC响应式设计指南

## 1. 响应式设计原则

### 1.1 移动优先
采用移动优先的设计策略，首先为小屏幕设备设计，然后逐步增强到大屏幕设备。

### 1.2 渐进增强
从基本功能开始，逐步添加更复杂的特性和视觉效果，确保所有用户都能访问核心功能。

### 1.3 灵活布局
使用弹性布局和相对单位，使界面能够适应不同屏幕尺寸和方向。

### 1.4 内容优先
确保重要内容在所有设备上都能清晰展示，优先级高的内容应首先呈现。

## 2. 断点系统

### 2.1 断点定义
基于用户设备使用情况和设计需求，定义以下断点：

```scss
// 响应式断点
$breakpoint-xs: 0;          // 超小屏设备 (0-575px)
$breakpoint-sm: 576px;      // 小屏设备 (576px-767px)
$breakpoint-md: 768px;      // 中屏设备 (768px-991px)
$breakpoint-lg: 992px;      // 大屏设备 (992px-1199px)
$breakpoint-xl: 1200px;     // 超大屏设备 (1200px-1599px)
$breakpoint-xxl: 1600px;    // 特大屏设备 (1600px+)

// 媒体查询混入
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: $breakpoint-sm - 1) { @content; }
  } @else if $breakpoint == sm {
    @media (min-width: $breakpoint-sm) and (max-width: $breakpoint-md - 1) { @content; }
  } @else if $breakpoint == md {
    @media (min-width: $breakpoint-md) and (max-width: $breakpoint-lg - 1) { @content; }
  } @else if $breakpoint == lg {
    @media (min-width: $breakpoint-lg) and (max-width: $breakpoint-xl - 1) { @content; }
  } @else if $breakpoint == xl {
    @media (min-width: $breakpoint-xl) and (max-width: $breakpoint-xxl - 1) { @content; }
  } @else if $breakpoint == xxl {
    @media (min-width: $breakpoint-xxl) { @content; }
  }
}

// 移动优先混入
@mixin respond-up($breakpoint) {
  @if $breakpoint == sm {
    @media (min-width: $breakpoint-sm) { @content; }
  } @else if $breakpoint == md {
    @media (min-width: $breakpoint-md) { @content; }
  } @else if $breakpoint == lg {
    @media (min-width: $breakpoint-lg) { @content; }
  } @else if $breakpoint == xl {
    @media (min-width: $breakpoint-xl) { @content; }
  } @else if $breakpoint == xxl {
    @media (min-width: $breakpoint-xxl) { @content; }
  }
}
```

### 2.2 设备分类
根据不同断点，将设备分类如下：

1. **手机设备** (xs, sm): 0-767px
2. **平板设备** (md): 768px-991px
3. **桌面设备** (lg, xl, xxl): 992px+

## 3. 布局策略

### 3.1 弹性网格系统
使用基于Flexbox的网格系统，支持灵活的布局调整。

```scss
// 弹性网格容器
.grid-container {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -$spacing-sm; // 负边距补偿
}

// 弹性网格项
.grid-item {
  padding: $spacing-sm;
  flex: 1 1 100%; // 默认占满一行
  
  // 小屏设备 (≥576px)
  @include respond-up(sm) {
    flex: 1 1 50%; // 两列布局
  }
  
  // 中屏设备 (≥768px)
  @include respond-up(md) {
    flex: 1 1 33.333%; // 三列布局
  }
  
  // 大屏设备 (≥992px)
  @include respond-up(lg) {
    flex: 1 1 25%; // 四列布局
  }
}
```

### 3.2 流体布局
使用百分比和最大宽度创建流体布局。

```scss
// 流体容器
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 $spacing-md;
  
  // 超小屏设备
  @include respond-to(xs) {
    padding: 0 $spacing-sm;
  }
}

// 流体图像
.fluid-image {
  max-width: 100%;
  height: auto;
  display: block;
}
```

### 3.3 弹性组件
创建能够适应不同屏幕尺寸的弹性组件。

```scss
// 弹性卡片
.flexible-card {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .card-header {
    flex-shrink: 0;
  }
  
  .card-body {
    flex: 1 1 auto;
  }
  
  .card-footer {
    flex-shrink: 0;
  }
}
```

## 4. 导航设计

### 4.1 桌面端导航
在桌面设备上使用水平导航栏。

```vue
<template>
  <nav class="desktop-navigation" role="navigation">
    <div class="nav-container">
      <div class="nav-brand">
        <router-link to="/" class="brand-link">
          <img src="/logo.png" alt="ACRAC Logo" class="brand-logo">
          <span class="brand-name">ACRAC</span>
        </router-link>
      </div>
      
      <ul class="nav-menu" role="menubar">
        <li 
          v-for="item in navigationItems" 
          :key="item.path"
          class="nav-item"
          role="none"
        >
          <router-link 
            :to="item.path"
            class="nav-link"
            :class="{ 'nav-link--active': isActive(item.path) }"
            role="menuitem"
          >
            {{ item.label }}
          </router-link>
        </li>
      </ul>
      
      <div class="nav-actions">
        <button 
          class="search-toggle"
          @click="toggleSearch"
          aria-label="打开搜索"
        >
          <svg class="icon"><use xlink:href="#icon-search"></use></svg>
        </button>
        
        <div class="user-menu">
          <button 
            class="user-toggle"
            @click="toggleUserMenu"
            aria-haspopup="true"
            aria-expanded="false"
          >
            <img :src="user.avatar" :alt="user.name" class="user-avatar">
          </button>
        </div>
      </div>
    </div>
  </nav>
</template>
```

### 4.2 移动端导航
在移动设备上使用汉堡菜单和侧滑面板。

```vue
<template>
  <nav class="mobile-navigation" role="navigation">
    <div class="nav-container">
      <div class="nav-brand">
        <router-link to="/" class="brand-link">
          <img src="/logo.png" alt="ACRAC Logo" class="brand-logo">
          <span class="brand-name">ACRAC</span>
        </router-link>
      </div>
      
      <button 
        class="menu-toggle"
        @click="toggleMenu"
        :aria-expanded="isMenuOpen"
        aria-label="切换导航菜单"
        aria-controls="mobile-menu"
      >
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
        <span class="hamburger-line"></span>
      </button>
    </div>
    
    <div 
      v-if="isMenuOpen"
      id="mobile-menu"
      class="mobile-menu-panel"
      role="dialog"
      aria-modal="true"
      aria-labelledby="menu-title"
    >
      <div class="menu-overlay" @click="closeMenu"></div>
      <div class="menu-content">
        <h2 id="menu-title" class="sr-only">主导航菜单</h2>
        
        <div class="menu-header">
          <div class="user-info">
            <img :src="user.avatar" :alt="user.name" class="user-avatar">
            <span class="user-name">{{ user.name }}</span>
          </div>
          <button 
            class="menu-close"
            @click="closeMenu"
            aria-label="关闭菜单"
          >
            ×
          </button>
        </div>
        
        <ul class="menu-list" role="menubar">
          <li 
            v-for="item in navigationItems" 
            :key="item.path"
            class="menu-item"
            role="none"
          >
            <router-link 
              :to="item.path"
              class="menu-link"
              :class="{ 'menu-link--active': isActive(item.path) }"
              role="menuitem"
              @click="closeMenu"
            >
              <svg class="menu-icon"><use :xlink:href="`#icon-${item.icon}`"></use></svg>
              <span>{{ item.label }}</span>
            </router-link>
          </li>
        </ul>
        
        <div class="menu-footer">
          <button class="menu-action" @click="handleSettings">
            <svg class="action-icon"><use xlink:href="#icon-settings"></use></svg>
            <span>设置</span>
          </button>
          <button class="menu-action" @click="handleLogout">
            <svg class="action-icon"><use xlink:href="#icon-logout"></use></svg>
            <span>退出</span>
          </button>
        </div>
      </div>
    </div>
  </nav>
</template>
```

### 4.3 底部导航（移动端）
在移动设备上提供底部导航栏。

```vue
<template>
  <nav 
    v-if="isMobile"
    class="bottom-navigation"
    role="navigation"
    aria-label="底部导航"
  >
    <ul class="nav-list">
      <li 
        v-for="item in bottomNavItems" 
        :key="item.path"
        class="nav-item"
      >
        <router-link 
          :to="item.path"
          class="nav-button"
          :class="{ 'nav-button--active': isActive(item.path) }"
          :aria-current="isActive(item.path) ? 'page' : undefined"
        >
          <svg class="nav-icon"><use :xlink:href="`#icon-${item.icon}`"></use></svg>
          <span class="nav-label">{{ item.label }}</span>
        </router-link>
      </li>
    </ul>
  </nav>
</template>
```

## 5. 图片和媒体响应式处理

### 5.1 响应式图片
使用srcset和sizes属性提供不同尺寸的图片。

```vue
<template>
  <img 
    :src="imageSrc"
    :srcset="`${imageSrcSmall} 480w, ${imageSrcMedium} 768w, ${imageSrcLarge} 1200w`"
    :sizes="(isMobile) ? '100vw' : '(max-width: 768px) 50vw, 33vw'"
    :alt="imageAlt"
    class="responsive-image"
  >
</template>

<script>
export default {
  data() {
    return {
      imageSrc: '/images/default.jpg',
      imageSrcSmall: '/images/default-480.jpg',
      imageSrcMedium: '/images/default-768.jpg',
      imageSrcLarge: '/images/default-1200.jpg',
      imageAlt: '描述图片内容'
    }
  },
  
  computed: {
    isMobile() {
      return window.innerWidth < 768;
    }
  }
}
</script>
```

### 5.2 视频响应式处理
确保视频在不同设备上正确显示。

```scss
// 响应式视频容器
.video-container {
  position: relative;
  padding-bottom: 56.25%; // 16:9 宽高比
  height: 0;
  overflow: hidden;
  
  iframe,
  video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}
```

## 6. 表单响应式设计

### 6.1 表单布局
根据屏幕尺寸调整表单布局。

```vue
<template>
  <form class="responsive-form">
    <div class="form-row">
      <div class="form-group">
        <label for="firstName">名字</label>
        <input 
          type="text" 
          id="firstName" 
          name="firstName"
          class="form-control"
        >
      </div>
      
      <div class="form-group">
        <label for="lastName">姓氏</label>
        <input 
          type="text" 
          id="lastName" 
          name="lastName"
          class="form-control"
        >
      </div>
    </div>
    
    <div class="form-group">
      <label for="email">邮箱</label>
      <input 
        type="email" 
        id="email" 
        name="email"
        class="form-control"
      >
    </div>
    
    <div class="form-group">
      <label for="message">消息</label>
      <textarea 
        id="message" 
        name="message"
        rows="4"
        class="form-control"
      ></textarea>
    </div>
    
    <button type="submit" class="btn btn-primary">提交</button>
  </form>
</template>

<style scoped>
.responsive-form {
  .form-row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -$spacing-sm;
    
    .form-group {
      padding: 0 $spacing-sm;
      flex: 1 1 100%;
      margin-bottom: $spacing-md;
      
      @include respond-up(md) {
        flex: 1 1 50%;
      }
    }
  }
  
  .form-group {
    margin-bottom: $spacing-md;
    
    label {
      display: block;
      margin-bottom: $spacing-xs;
      font-weight: $font-weight-medium;
    }
    
    .form-control {
      width: 100%;
      padding: $spacing-sm;
      border: 1px solid $gray-300;
      border-radius: $border-radius-sm;
      
      @include respond-to(xs) {
        padding: $spacing-xs;
      }
    }
  }
  
  .btn {
    width: 100%;
    
    @include respond-up(md) {
      width: auto;
      padding: $spacing-sm $spacing-lg;
    }
  }
}
</style>
```

## 7. 表格响应式处理

### 7.1 桌面端表格
在桌面设备上使用标准表格布局。

```vue
<template>
  <div class="desktop-table-container">
    <table class="data-table">
      <thead>
        <tr>
          <th>姓名</th>
          <th>科室</th>
          <th>职位</th>
          <th>邮箱</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="user in users" :key="user.id">
          <td>{{ user.name }}</td>
          <td>{{ user.department }}</td>
          <td>{{ user.position }}</td>
          <td>{{ user.email }}</td>
          <td>
            <button class="btn btn-sm btn-primary">编辑</button>
            <button class="btn btn-sm btn-danger">删除</button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
```

### 7.2 移动端表格
在移动设备上使用卡片式布局替代表格。

```vue
<template>
  <div class="mobile-table-container">
    <div 
      v-for="user in users" 
      :key="user.id"
      class="table-card"
    >
      <div class="card-row">
        <span class="row-label">姓名:</span>
        <span class="row-value">{{ user.name }}</span>
      </div>
      
      <div class="card-row">
        <span class="row-label">科室:</span>
        <span class="row-value">{{ user.department }}</span>
      </div>
      
      <div class="card-row">
        <span class="row-label">职位:</span>
        <span class="row-value">{{ user.position }}</span>
      </div>
      
      <div class="card-row">
        <span class="row-label">邮箱:</span>
        <span class="row-value">{{ user.email }}</span>
      </div>
      
      <div class="card-actions">
        <button class="btn btn-sm btn-primary">编辑</button>
        <button class="btn btn-sm btn-danger">删除</button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.mobile-table-container {
  .table-card {
    background: $white;
    border: 1px solid $gray-200;
    border-radius: $border-radius-md;
    padding: $spacing-md;
    margin-bottom: $spacing-md;
    
    .card-row {
      display: flex;
      margin-bottom: $spacing-sm;
      
      .row-label {
        flex: 0 0 80px;
        font-weight: $font-weight-medium;
        color: $gray-700;
      }
      
      .row-value {
        flex: 1;
        color: $gray-900;
      }
    }
    
    .card-actions {
      display: flex;
      gap: $spacing-sm;
      margin-top: $spacing-md;
      
      .btn {
        flex: 1;
      }
    }
  }
}
</style>
```

## 8. 触摸优化

### 8.1 触摸目标大小
确保触摸目标足够大，便于手指操作。

```scss
// 触摸友好的按钮
.touch-button {
  min-height: 44px;
  min-width: 44px;
  padding: $spacing-md;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  
  // 非触摸设备可以使用较小的按钮
  @media (hover: hover) {
    min-height: 36px;
    min-width: 36px;
    padding: $spacing-sm;
  }
}

// 触摸友好的链接
.touch-link {
  padding: $spacing-sm;
  margin: -$spacing-sm;
  display: inline-block;
  
  @media (hover: hover) {
    padding: $spacing-xs;
    margin: -$spacing-xs;
  }
}
```

### 8.2 手势支持
为常用操作添加手势支持。

```vue
<template>
  <div 
    class="swipeable-container"
    @touchstart="handleTouchStart"
    @touchmove="handleTouchMove"
    @touchend="handleTouchEnd"
  >
    <div 
      class="swipeable-content"
      :style="{ transform: `translateX(${swipeOffset}px)` }"
    >
      <!-- 内容 -->
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      touchStartX: 0,
      touchStartY: 0,
      swipeOffset: 0,
      isSwiping: false
    }
  },
  
  methods: {
    handleTouchStart(event) {
      this.touchStartX = event.touches[0].clientX;
      this.touchStartY = event.touches[0].clientY;
      this.isSwiping = true;
    },
    
    handleTouchMove(event) {
      if (!this.isSwiping) return;
      
      const touchX = event.touches[0].clientX;
      const touchY = event.touches[0].clientY;
      
      // 计算水平和垂直移动距离
      const deltaX = touchX - this.touchStartX;
      const deltaY = touchY - this.touchStartY;
      
      // 如果水平移动大于垂直移动，则认为是水平滑动
      if (Math.abs(deltaX) > Math.abs(deltaY)) {
        event.preventDefault();
        this.swipeOffset = deltaX;
      }
    },
    
    handleTouchEnd() {
      if (!this.isSwiping) return;
      
      // 根据滑动距离决定操作
      if (Math.abs(this.swipeOffset) > 50) {
        if (this.swipeOffset > 0) {
          this.swipeRight();
        } else {
          this.swipeLeft();
        }
      }
      
      // 重置状态
      this.swipeOffset = 0;
      this.isSwiping = false;
    },
    
    swipeLeft() {
      // 向左滑动操作
      console.log('向左滑动');
    },
    
    swipeRight() {
      // 向右滑动操作
      console.log('向右滑动');
    }
  }
}
</script>
```

## 9. 性能优化

### 9.1 条件加载
根据设备特性条件加载资源。

```vue
<template>
  <div class="responsive-component">
    <!-- 高分辨率设备加载高清图片 -->
    <picture>
      <source 
        media="(min-resolution: 2dppx)" 
        :srcset="`${imageSrc2x} 2x, ${imageSrc3x} 3x`"
      >
      <img :src="imageSrc" :alt="imageAlt">
    </picture>
    
    <!-- 大屏设备加载复杂组件 -->
    <complex-component v-if="!isMobile" />
    
    <!-- 移动设备使用简化版本 -->
    <simple-component v-else />
  </div>
</template>

<script>
export default {
  data() {
    return {
      imageSrc: '/images/default.jpg',
      imageSrc2x: '/images/<EMAIL>',
      imageSrc3x: '/images/<EMAIL>',
      imageAlt: '描述图片内容'
    }
  },
  
  computed: {
    isMobile() {
      // 简化的移动端检测
      return window.innerWidth < 768;
    }
  }
}
</script>
```

### 9.2 懒加载
为非关键资源实现懒加载。

```vue
<template>
  <div class="lazy-container">
    <img 
      v-for="image in images" 
      :key="image.id"
      :data-src="image.src"
      :alt="image.alt"
      class="lazy-image"
      loading="lazy"
    >
  </div>
</template>

<script>
export default {
  mounted() {
    // 使用Intersection Observer实现懒加载
    if ('IntersectionObserver' in window) {
      const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const img = entry.target;
            img.src = img.dataset.src;
            img.classList.remove('lazy-image');
            observer.unobserve(img);
          }
        });
      });
      
      document.querySelectorAll('.lazy-image').forEach(img => {
        imageObserver.observe(img);
      });
    }
  }
}
</script>
```

## 10. 测试策略

### 10.1 设备测试
在真实设备上进行测试，确保响应式效果。

```markdown
## 响应式测试设备清单

### 手机设备
- iPhone 14 Pro (393×852)
- iPhone SE (375×667)
- Samsung Galaxy S23 (360×780)
- Google Pixel 7 (412×915)

### 平板设备
- iPad Air (820×1180)
- iPad Pro 11" (834×1194)
- Samsung Galaxy Tab S8 (800×1280)

### 桌面设备
- 13" 笔记本 (1280×800)
- 15" 笔记本 (1920×1080)
- 24" 显示器 (1920×1200)
- 27" 显示器 (2560×1440)
- 32" 显示器 (3840×2160)
```

### 10.2 浏览器测试
在不同浏览器中测试响应式效果。

```markdown
## 浏览器兼容性测试

### 移动浏览器
- Safari (iOS)
- Chrome (Android)
- Firefox (Android)
- Samsung Internet

### 桌面浏览器
- Chrome (最新2个版本)
- Firefox (最新2个版本)
- Safari (最新2个版本)
- Edge (最新2个版本)
```

通过遵循这些响应式设计指南，ACRAC系统将能够在各种设备上提供一致且优质的用户体验。