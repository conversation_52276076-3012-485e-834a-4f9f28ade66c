# ACRAC部署指南

## 1. 部署架构

### 1.1 系统架构概述
ACRAC医疗影像智能推荐系统采用现代化的微服务架构，包含以下核心组件：

1. **前端应用**：Vue 3 + TypeScript + Vite构建的单页应用
2. **后端API**：FastAPI构建的RESTful API服务
3. **数据库**：PostgreSQL + pgvector向量数据库
4. **AI服务**：SiliconFlow API + Ollama本地LLM服务
5. **搜索服务**：Elasticsearch向量搜索（可选）
6. **缓存服务**：Redis缓存服务
7. **消息队列**：RabbitMQ异步任务处理
8. **监控服务**：Prometheus + Grafana监控系统

### 1.2 部署环境要求

#### 生产环境最低配置
- **CPU**：4核
- **内存**：16GB
- **存储**：100GB SSD
- **网络**：100Mbps带宽
- **操作系统**：Ubuntu 20.04 LTS / CentOS 8

#### 推荐配置
- **CPU**：8核
- **内存**：32GB
- **存储**：500GB SSD
- **网络**：1Gbps带宽
- **操作系统**：Ubuntu 22.04 LTS

## 2. 部署方式

### 2.1 Docker Compose部署（推荐）

#### docker-compose.yml配置
```yaml
version: '3.8'

services:
  # 数据库服务
  postgres:
    image: pgvector/pgvector:pg15
    container_name: acrac-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: acrac
      POSTGRES_USER: acrac_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U acrac_user -d acrac"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - acrac-network

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: acrac-redis
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - acrac-network

  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: acrac-backend
    restart: unless-stopped
    environment:
      - DATABASE_URL=postgresql://acrac_user:${POSTGRES_PASSWORD}@postgres:5432/acrac
      - REDIS_URL=redis://redis:6379/0
      - SILICONFLOW_API_KEY=${SILICONFLOW_API_KEY}
      - OLLAMA_BASE_URL=http://ollama:11434
      - SECRET_KEY=${SECRET_KEY}
      - ENVIRONMENT=production
    ports:
      - "8000:8000"
    volumes:
      - ./backend/app:/app/app
      - backend_logs:/app/logs
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - acrac-network

  # Ollama AI服务
  ollama:
    image: ollama/ollama:latest
    container_name: acrac-ollama
    restart: unless-stopped
    volumes:
      - ollama_data:/root/.ollama
    ports:
      - "11434:11434"
    networks:
      - acrac-network

  # 前端应用服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: acrac-frontend
    restart: unless-stopped
    environment:
      - VITE_API_BASE_URL=http://backend:8000
      - NODE_ENV=production
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - frontend_logs:/var/log/nginx
    depends_on:
      - backend
    networks:
      - acrac-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: acrac-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/conf.d:/etc/nginx/conf.d
      - ./nginx/certs:/etc/nginx/certs
      - frontend_static:/usr/share/nginx/html
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - acrac-network

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: acrac-prometheus
    restart: unless-stopped
    volumes:
      - ./prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - acrac-network

  # 监控服务 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: acrac-grafana
    restart: unless-stopped
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD}
    volumes:
      - grafana_data:/var/lib/grafana
    ports:
      - "3000:3000"
    depends_on:
      - prometheus
    networks:
      - acrac-network

volumes:
  postgres_data:
  redis_data:
  ollama_data:
  backend_logs:
  frontend_logs:
  nginx_logs:
  frontend_static:
  prometheus_data:
  grafana_data:

networks:
  acrac-network:
    driver: bridge
```

### 2.2 环境变量配置

#### .env文件
```bash
# 数据库配置
POSTGRES_PASSWORD=your_secure_password
POSTGRES_DB=acrac
POSTGRES_USER=acrac_user

# AI服务配置
SILICONFLOW_API_KEY=your_siliconflow_api_key
OLLAMA_MODEL=qwen2:7b

# 安全配置
SECRET_KEY=your_very_secure_secret_key_here
JWT_SECRET_KEY=your_jwt_secret_key_here

# 监控配置
GRAFANA_PASSWORD=your_grafana_admin_password

# 邮件配置（可选）
SMTP_HOST=smtp.example.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_email_password

# 日志配置
LOG_LEVEL=INFO
LOG_FILE=/app/logs/acrac.log
```

## 3. 部署步骤

### 3.1 前置准备

#### 系统环境准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 添加当前用户到docker组
sudo usermod -aG docker $USER

# 重启系统使组权限生效
sudo reboot
```

#### 项目代码获取
```bash
# 克隆项目代码
git clone https://github.com/your-org/acrac-web.git
cd acrac-web

# 切换到稳定分支
git checkout production
```

#### 环境变量配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
nano .env

# 设置安全的密码和密钥
openssl rand -base64 32  # 生成SECRET_KEY
openssl rand -base64 32  # 生成JWT_SECRET_KEY
```

### 3.2 数据库初始化

#### 初始化脚本
```sql
-- init-scripts/01-init.sql
-- 创建数据库扩展
CREATE EXTENSION IF NOT EXISTS vector;

-- 创建必要的索引
CREATE INDEX IF NOT EXISTS idx_panels_code ON panels(code);
CREATE INDEX IF NOT EXISTS idx_topics_code ON topics(code);
CREATE INDEX IF NOT EXISTS idx_scenarios_code ON clinical_scenarios(code);
CREATE INDEX IF NOT EXISTS idx_procedures_code ON procedure_dictionaries(code);

-- 创建向量索引
CREATE INDEX IF NOT EXISTS idx_panels_embedding ON panels 
  USING ivfflat (embedding vector_cosine_ops)
  WITH (lists = 100);

CREATE INDEX IF NOT EXISTS idx_procedures_embedding ON procedure_dictionaries 
  USING ivfflat (embedding vector_cosine_ops)
  WITH (lists = 100);
```

### 3.3 AI模型准备

#### Ollama模型拉取
```bash
# 启动Ollama服务
docker-compose up -d ollama

# 拉取Qwen2模型
docker-compose exec ollama ollama pull qwen2:7b

# 验证模型
docker-compose exec ollama ollama list
```

### 3.4 应用部署

#### 构建和启动服务
```bash
# 构建所有服务
docker-compose build

# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 数据库迁移
```bash
# 运行数据库迁移
docker-compose exec backend alembic upgrade head

# 初始化系统数据
docker-compose exec backend python -m app.scripts.initialize_data
```

#### 向量数据库构建
```bash
# 构建向量索引
docker-compose exec backend python -m app.scripts.build_vector_index

# 验证向量索引
docker-compose exec backend python -m app.scripts.verify_vector_index
```

## 4. 配置优化

### 4.1 Nginx配置优化

#### nginx/conf.d/acrac.conf
```nginx
upstream backend {
    server backend:8000;
}

upstream frontend {
    server frontend:80;
}

server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /etc/nginx/certs/fullchain.pem;
    ssl_certificate_key /etc/nginx/certs/privkey.pem;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    
    # gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;
    
    # 静态资源缓存
    location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # API代理
    location /api/ {
        proxy_pass http://backend/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # 前端应用
    location / {
        proxy_pass http://frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

### 4.2 PostgreSQL性能优化

#### postgres.conf优化
```bash
# 内存相关配置
shared_buffers = 2GB
effective_cache_size = 6GB
work_mem = 16MB
maintenance_work_mem = 512MB

# 并行处理
max_worker_processes = 8
max_parallel_workers_per_gather = 4
max_parallel_workers = 8

# WAL配置
wal_buffers = 16MB
min_wal_size = 2GB
max_wal_size = 8GB

# 查询优化
random_page_cost = 1.1
seq_page_cost = 1.0

# 连接配置
max_connections = 200
superuser_reserved_connections = 3

# 向量扩展配置
shared_preload_libraries = 'pg_stat_statements, vector'
```

## 5. 监控和日志

### 5.1 Prometheus监控配置

#### prometheus/prometheus.yml
```yaml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  - job_name: 'backend'
    static_configs:
      - targets: ['backend:8000']
    metrics_path: '/metrics'

  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
```

### 5.2 日志管理

#### 日志轮转配置
```bash
# /etc/logrotate.d/acrac
/var/log/acrac/*.log {
    daily
    rotate 30
    compress
    delaycompress
    missingok
    notifempty
    create 644 acrac acrac
    postrotate
        systemctl reload acrac-backend.service >/dev/null 2>&1 || true
    endscript
}
```

## 6. 安全配置

### 6.1 防火墙配置
```bash
# UFW防火墙配置
sudo ufw default deny incoming
sudo ufw default allow outgoing

# 允许必要端口
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 9090/tcp  # Prometheus
sudo ufw allow 3000/tcp  # Grafana

# 启用防火墙
sudo ufw enable
```

### 6.2 SSL证书配置
```bash
# 使用Let's Encrypt获取SSL证书
sudo apt install certbot python3-certbot-nginx

# 获取证书
sudo certbot --nginx -d your-domain.com

# 自动续期
sudo crontab -e
# 添加以下行
0 12 * * * /usr/bin/certbot renew --quiet
```

## 7. 备份和恢复

### 7.1 数据库备份
```bash
# 创建备份脚本
#!/bin/bash
# backup.sh

BACKUP_DIR="/backup"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/acrac_backup_$DATE.sql"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 数据库备份
docker-compose exec postgres pg_dump -U acrac_user -d acrac > $BACKUP_FILE

# 压缩备份文件
gzip $BACKUP_FILE

# 删除7天前的备份
find $BACKUP_DIR -name "acrac_backup_*.sql.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_FILE.gz"
```

### 7.2 数据恢复
```bash
# 恢复数据库
gunzip acrac_backup_20231201_120000.sql.gz
docker-compose exec -T postgres psql -U acrac_user -d acrac < acrac_backup_20231201_120000.sql
```

## 8. 故障排除

### 8.1 常见问题诊断

#### 服务启动失败
```bash
# 查看服务状态
docker-compose ps

# 查看详细日志
docker-compose logs backend

# 进入容器调试
docker-compose exec backend /bin/bash
```

#### 数据库连接问题
```bash
# 测试数据库连接
docker-compose exec backend python -c "
import psycopg2
try:
    conn = psycopg2.connect(
        host='postgres',
        database='acrac',
        user='acrac_user',
        password='your_password'
    )
    print('Database connection successful')
    conn.close()
except Exception as e:
    print(f'Database connection failed: {e}')
"
```

#### AI服务问题
```bash
# 测试Ollama服务
curl http://localhost:11434/api/tags

# 测试模型响应
curl http://localhost:11434/api/generate -d '{
  "model": "qwen2:7b",
  "prompt": "Hello, how are you?",
  "stream": false
}'
```

### 8.2 性能调优

#### 系统资源监控
```bash
# 实时监控
docker stats

# 系统资源使用情况
htop
iotop
```

#### 数据库性能分析
```sql
-- 查看慢查询
SELECT query, mean_time, calls 
FROM pg_stat_statements 
ORDER BY mean_time DESC 
LIMIT 10;

-- 查看表大小
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
```

通过遵循这套部署指南，ACRAC系统将能够在生产环境中稳定运行，为医疗机构提供可靠的智能推荐服务。