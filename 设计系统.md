# ACRAC设计系统

## 1. 设计理念

### 1.1 核心理念
ACRAC设计系统以"专业、简洁、高效"为核心设计理念，致力于为医疗专业人士提供优质的用户体验。

### 1.2 设计原则
1. **专业性**：体现医疗行业的专业特征
2. **易用性**：简化操作流程，提升使用效率
3. **一致性**：统一的视觉语言和交互模式
4. **可访问性**：支持各类用户群体的使用需求

## 2. 颜色系统

### 2.1 品牌色彩
#### 主色 - 医疗蓝
- 主色：#2E86AB（深海蓝）
- 浅色：#5BA3C7（天空蓝）
- 深色：#1A5F80（深海蓝）

#### 功能色彩
- 成功色：#28A745（医疗绿）
- 警告色：#FFC107（警示黄）
- 错误色：#DC3545（危险红）
- 信息色：#17A2B8（信息蓝）

#### 中性色彩
- 白色：#FFFFFF
- 灰色系列：
  - 浅灰1：#F8F9FA
  - 浅灰2：#E9ECEF
  - 浅灰3：#DEE2E6
  - 中灰1：#CED4DA
  - 中灰2：#ADB5BD
  - 深灰1：#6C757D
  - 深灰2：#495057
  - 深灰3：#343A40
  - 黑色：#212529

### 2.2 专业色彩
#### 适宜性色彩
- 通常适宜：#28A745（医疗绿）
- 可能适宜：#FFC107（警示黄）
- 通常不适宜：#DC3545（危险红）

#### 状态色彩
- 待审核：#6C757D（中灰）
- 已通过：#28A745（医疗绿）
- 已拒绝：#DC3545（危险红）
- 处理中：#17A2B8（信息蓝）

### 2.3 颜色使用规范
#### 文字颜色
- 主要文字：#212529
- 次要文字：#495057
- 辅助文字：#6C757D
- 占位文字：#ADB5BD

#### 背景颜色
- 主要背景：#FFFFFF
- 次要背景：#F8F9FA
- 强调背景：#E9ECEF

#### 边框颜色
- 主要边框：#DEE2E6
- 次要边框：#CED4DA
- 强调边框：#2E86AB

## 3. 字体系统

### 3.1 字体族
#### 中文字体
- 主要字体：'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif
- 备选字体：'Helvetica Neue', Helvetica, Arial, sans-serif

#### 英文字体
- 主要字体：'Roboto', 'Helvetica Neue', Helvetica, Arial, sans-serif
- 等宽字体：'Monaco', 'Consolas', 'Courier New', monospace

### 3.2 字体层级
| 层级 | 字体大小 | 字体粗细 | 行高 | 使用场景 |
|------|---------|---------|------|---------|
| h1 | 32px | 700 | 1.2 | 页面主标题 |
| h2 | 28px | 600 | 1.3 | 页面副标题 |
| h3 | 24px | 600 | 1.3 | 模块标题 |
| h4 | 20px | 500 | 1.4 | 小节标题 |
| body-large | 18px | 400 | 1.5 | 大段正文 |
| body | 16px | 400 | 1.5 | 普通正文 |
| body-small | 14px | 400 | 1.4 | 辅助文字 |
| caption | 12px | 400 | 1.4 | 注释文字 |

### 3.3 字体使用规范
#### 标题使用
- 页面标题使用h1层级
- 模块标题使用h2-h3层级
- 小节标题使用h4层级

#### 正文使用
- 主要内容使用body层级
- 辅助信息使用body-small层级
- 注释说明使用caption层级

## 4. 间距系统

### 4.1 网格系统
基于8px网格系统，所有间距应为8的倍数：
- xs: 4px
- sm: 8px
- md: 16px
- lg: 24px
- xl: 32px
- 2xl: 48px
- 3xl: 64px

### 4.2 内边距规范
#### 容器内边距
- 小型容器：16px
- 中型容器：24px
- 大型容器：32px

#### 组件内边距
- 按钮：水平16px，垂直8px
- 输入框：水平12px，垂直8px
- 卡片：24px

### 4.3 外边距规范
#### 模块间距
- 相关模块：16px
- 独立模块：24px
- 页面区块：32px

#### 元素间距
- 表单项：16px
- 列表项：12px
- 按钮组：8px

## 5. 阴影系统

### 5.1 阴影层级
#### 浮层阴影
- 浅层：0 1px 2px 0 rgba(0, 0, 0, 0.05)
- 中层：0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)
- 深层：0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)

#### 特殊阴影
- 悬浮阴影：0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)
- 强调阴影：0 25px 50px -12px rgba(0, 0, 0, 0.25)

### 5.2 阴影使用场景
#### 卡片组件
- 默认状态：浅层阴影
- 悬停状态：中层阴影
- 选中状态：深层阴影

#### 弹窗组件
- 模态框：深层阴影
- 工具提示：浅层阴影

#### 导航组件
- 侧边栏：中层阴影
- 下拉菜单：浅层阴影

## 6. 圆角系统

### 6.1 圆角规范
- 小圆角：4px - 用于按钮、标签等小型组件
- 中圆角：8px - 用于卡片、表单等中型组件
- 大圆角：12px - 用于模态框、面板等大型组件
- 超大圆角：16px - 用于特殊展示组件

### 6.2 圆角使用场景
#### 按钮组件
- 普通按钮：4px
- 圆角按钮：20px（高度的一半）

#### 卡片组件
- 普通卡片：8px
- 特殊卡片：12px

#### 表单组件
- 输入框：4px
- 下拉框：4px
- 复选框：4px

## 7. 图标系统

### 7.1 图标规范
#### 图标尺寸
- 小型图标：16px × 16px
- 中型图标：20px × 20px
- 大型图标：24px × 24px
- 超大图标：32px × 32px

#### 图标颜色
- 主要图标：#212529
- 次要图标：#495057
- 辅助图标：#6C757D
- 功能图标：根据功能使用对应功能色

### 7.2 图标使用规范
#### 导航图标
- 侧边栏导航：24px
- 顶部导航：20px
- 面包屑导航：16px

#### 操作图标
- 按钮图标：16px
- 表格操作：16px
- 表单操作：20px

#### 状态图标
- 成功状态：绿色对勾
- 警告状态：黄色感叹号
- 错误状态：红色叉号
- 信息状态：蓝色信息号

## 8. 响应式设计

### 8.1 断点系统
| 断点 | 尺寸范围 | 设备类型 | 容器宽度 |
|------|---------|---------|---------|
| xs | < 576px | 手机 | auto |
| sm | 576px - 767px | 大手机 | 540px |
| md | 768px - 991px | 平板 | 720px |
| lg | 992px - 1199px | 小桌面 | 960px |
| xl | 1200px - 1399px | 桌面 | 1140px |
| xxl | ≥ 1400px | 大桌面 | 1320px |

### 8.2 响应式策略
#### 移动优先
- 优先设计移动端体验
- 逐步增强桌面端功能
- 确保核心功能在移动端可用

#### 弹性布局
- 使用Flexbox和Grid布局
- 支持内容自适应
- 保持视觉层次清晰

#### 交互适配
- 触摸友好的点击区域（最小44px）
- 手势操作支持
- 简化移动端操作流程

## 9. 动效系统

### 9.1 动效原则
#### 自然性
- 动效应符合物理规律
- 过渡应平滑自然
- 避免突兀的变化

#### 目的性
- 动效应有明确的功能目的
- 提供状态反馈
- 引导用户注意力

#### 性能性
- 动效应流畅无卡顿
- 避免影响页面性能
- 支持减弱动画设置

### 9.2 动效规范
#### 进入动画
- 淡入：opacity 0 → 1 (200ms)
- 滑入：transform translate (300ms)
- 缩放：scale 0.8 → 1 (250ms)

#### 离开动画
- 淡出：opacity 1 → 0 (200ms)
- 滑出：transform translate (300ms)
- 缩放：scale 1 → 0.8 (250ms)

#### 状态变化
- 颜色变化：300ms
- 尺寸变化：250ms
- 位置变化：300ms

### 9.3 动效使用场景
#### 页面切换
- 使用淡入淡出效果
- 持续时间：300ms
- 缓动函数：ease-in-out

#### 组件交互
- 按钮点击：缩放效果
- 卡片悬停：上浮效果
- 表单验证：抖动效果

#### 数据加载
- 骨架屏：闪烁效果
- 进度条：线性增长
- 加载指示器：旋转效果

## 10. 可访问性

### 10.1 颜色对比度
#### 文字对比度
- 正常文字：对比度≥4.5:1
- 大号文字：对比度≥3:1
- 装饰性文字：对比度≥3:1

#### 交互元素对比度
- 按钮背景与文字：对比度≥4.5:1
- 链接与背景：对比度≥4.5:1
- 表单控件：对比度≥3:1

### 10.2 键盘导航
#### 焦点管理
- 所有交互元素应可键盘访问
- 焦点顺序应符合视觉顺序
- 焦点指示器应清晰可见

#### 快捷键
- 常用操作应支持快捷键
- 快捷键应在界面中提示
- 避免与系统快捷键冲突

### 10.3 屏幕阅读器支持
#### 语义化标签
- 使用正确的HTML语义标签
- 为非语义元素添加ARIA属性
- 提供适当的标题结构

#### 描述信息
- 为图标提供文字描述
- 为表单控件添加标签
- 为图片提供alt属性

## 11. 主题定制

### 11.1 主题变量
#### 颜色变量
```scss
// 主色调
--primary-color: #2E86AB;
--primary-light: #5BA3C7;
--primary-dark: #1A5F80;

// 功能色
--success-color: #28A745;
--warning-color: #FFC107;
--error-color: #DC3545;
--info-color: #17A2B8;
```

#### 字体变量
```scss
// 字体族
--font-family-base: 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
--font-family-code: 'Monaco', 'Consolas', 'Courier New', monospace;

// 字体大小
--font-size-base: 16px;
--font-size-large: 18px;
--font-size-small: 14px;
```

#### 间距变量
```scss
// 间距
--spacing-xs: 4px;
--spacing-sm: 8px;
--spacing-md: 16px;
--spacing-lg: 24px;
--spacing-xl: 32px;
```

### 11.2 主题切换
#### 明暗主题
- 明亮主题：默认主题
- 暗黑主题：深色背景配浅色文字
- 自动切换：根据系统设置自动切换

#### 专业主题
- 高对比度主题：增强颜色对比度
- 色盲友好主题：优化颜色搭配
- 简约主题：减少视觉干扰

## 12. 设计工具

### 12.1 设计软件
#### 主要工具
- Figma：界面设计和原型制作
- Sketch：矢量设计工具
- Adobe XD：用户体验设计

#### 辅助工具
- Zeplin：设计稿标注和协作
- Avocode：设计稿查看和导出
- Iconfont：图标管理

### 12.2 设计资源
#### 组件库
- Element Plus组件库
- 自定义组件库
- 图标库

#### 模板库
- 页面模板
- 组件模板
- 布局模板

### 12.3 设计规范
#### 文件管理
- 设计规范文档
- 组件使用指南
- 样式变量文档

#### 版本控制
- 设计稿版本管理
- 规范更新记录
- 变更通知机制