# RAGAS 评测页面规范文档

> 📋 **版本**: v1.2
> **最后更新**: 2024年12月
> **状态**: **修订完毕**

## 📑 目录
- [概述](#概述)
- [功能需求](#功能需求)
  - [数据源管理](#数据源管理)
  - [模型配置](#模型配置)
  - [评测流程](#评测流程)
  - [结果展示与追溯](#结果展示与追溯)
- [页面设计](#页面设计)
- [测试规范](#测试规范)
- [附录](#附录)

---

## 🎯 概述

### 📌 文档目的
本规范文档为 **RAGAS 评测页面** 的前端开发提供全面、统一的设计与实现标准，确保前后端数据交互的准确性，并提供强大的评测追溯能力。

### 🎯 核心目标
构建一个集成化、可追溯的 RAG 评测工具，支持：
- 📊 **多数据源导入**（数据库 + Excel/CSV）
- 🤖 **RAG 模型自动化评估**，基于 `Faithfulness`, `Answer Relevancy`, `Context Precision`, `Context Recall` 四大核心指标。
- 📈 **详细结果分析与可视化**，提供从宏观评分到微观步骤的全链路追溯。
- 💾 **评测快照保存与历史追溯**，实现对每一次评测过程的完整复现。

---

## 🛠️ 功能需求

### 📊 数据源管理

#### 🗄️ 数据库数据
| 功能项 | 详细要求 | 交互说明 |
|---|---|---|
| **数据连接** | 默认连接到后端服务关联的数据库。 | 前端无需配置，由后端管理。 |
| **数据预览** | - 默认显示数据库中最新的 100 条评测数据。<br>- 提供分页功能，允许用户浏览所有数据。 | 使用 `Table` 组件，支持分页。 |
| **数据刷新** | 提供一个“刷新”按钮，用于从数据库重新加载最新的数据列表。 | 点击按钮后，调用API获取新数据。 |

#### 📁 文件上传 (基于临床场景)
评测的核心是模拟真实问答场景，因此数据源将以包含多个“临床场景”的文件为基础。用户上传文件后，可针对这些场景输入并设定一个问题(`question`)来进行评测。

| 功能项 | 详细要求 | 交互说明与示例 |
|---|---|---|
| **文件格式** | 支持 `.xlsx` / `.xls` / `.csv`。 | 用户通过 `Upload` 组件选择文件。 |
| **数据结构** | 文件必须包含以下列：<br>- `题号` (可选, 用作唯一标识)<br>- `临床场景`<br>- `首选检查项目（标准化）` | `临床场景` 将作为RAG的 `context`。<br>`首选检查项目（标准化）` 将作为 `ground_truth`。 |
| **数据预览** | 上传成功后，文件内容应以表格形式展示在主内容区域，并提供**复选框**允许用户选择一行或多行进行评测。 | 使用 `Table` 组件，第一列为 `Checkbox`。 |
| **问题输入** | 在数据预览表格上方，提供一个独立的 `Input.TextArea` 组件，用于用户输入本次评测的 `question`。 | 这是评测的核心输入之一，不能为空。 |

### ⚙️ 模型配置
此区域允许用户配置用于 RAGAS 评测的大语言模型（LLM）。配置将保存在浏览器的 `localStorage` 中，以便持久化。

| 配置项 | 组件 | 详细要求 |
|---|---|---|
| **模型选择** | `Select` | - 提供一个预定义的模型列表（如 `gpt-4-turbo`, `deepseek-r1`, `qwen-max`）。<br>- 允许用户输入自定义模型名称。 |
| **API Key** | `Input.Password` | - 用于模型调用的 API 密钥。<br>- 存储在 `localStorage` 中，并进行安全提示。 |
| **Endpoint URL** | `Input` | - 模型的 API 端点地址。<br>- 提供默认值，但允许用户修改。 |
| **保存配置** | `Button` | 点击后，将上述配置保存到 `localStorage`，并给出成功提示。 |

### 🚀 评测流程
评测流程已更新，以适应“一个问题 VS 多个临床场景”的模式。

1.  **用户上传数据文件**：
    *   用户通过文件上传功能，上传包含 `临床场景` 和 `首选检查项目（标准化）` 的 `.xlsx` 文件。
    *   前端解析并以表格形式展示数据。

2.  **用户准备评测输入**：
    *   在独立的输入框中，**手动输入 `question`**。
    *   在数据表格中，**勾选一个或多个** 希望参与本次评测的临床场景（行）。

3.  **用户点击 [开始评测]**：
    *   前端收集用户输入的 `question`、所选中的临床场景数据（包含 `context` 和 `ground_truth`）以及模型配置。
    *   按钮状态变为“评测中...”，并禁用。

4.  **前端发送请求**：
    *   调用后端 `/api/ragas/evaluate` 端点，将 `question` 和场景列表作为 POST 请求的 body 发送。

5.  **后端执行评测**：
    *   后端接收请求，为**每一个**场景启动一个异步评测任务。
    *   任务遵循 `trace_five_cases.py` 中定义的完整链路。
    *   后端应提供一个任务 ID，以便前端轮询状态。

6.  **前端轮询与结果展示**：
    *   前端轮询任务状态。
    *   评测完成后，前端在“结果汇总”区域展示所有场景的**平均分**，并在“详细追溯”表格中**逐行展示**每个场景的独立评分和 `trace` 信息。
    *   [开始评测] 按钮恢复可用状态。

---

## 📈 结果展示与追溯

### 🎯 评测结果汇总 (Overall Score)
评测完成后，应在页面显著位置展示本次评测所有场景的 **平均综合评分** 和 **各项指标的平均分**。

```
┌───────────────────┬──────────┬─────────┐
│ 评估维度 (平均)   │ 得分     │ 评级    │
├───────────────────┼──────────┼─────────┤
│ Faithfulness      │ 0.90     │ 良好    │
│ Answer Relevancy  │ 0.88     │ 良好    │
│ Context Precision │ 0.92     │ 优秀    │
│ Context Recall    │ 0.91     │ 优秀    │
├───────────────────┼──────────┼─────────┤
│ **综合平均分**    │ **0.903**│ **优秀**│
└───────────────────┴──────────┴─────────┘
```
- **综合平均分计算**: 所有被评测场景的 `综合评分` 的平均值。

### 📜 评测内容追溯 (Traceability)
评测结果的核心是提供每个场景的详细追溯信息。前端应以表格形式，**逐行展示** 每个被评测场景的 `question`、`context`、`ground_truth`、各项评分以及操作。

| 题号 | Question | Context | Ground Truth | Faithfulness | Answer Relevancy | Context Precision | Context Recall | **综合分** | 操作 |
|---|---|---|---|---|---|---|---|---|---|
| CASE-001 | 检查建议? | "患者..." | "上腹部CT..." | 0.95 | 0.91 | 0.88 | 0.93 | **0.918** | [**查看追溯**] |
| CASE-002 | 检查建议? | "患者..." | "胸部CT..." | 0.85 | 0.82 | 0.90 | 0.88 | **0.863** | [**查看追溯**] |

- **查看追溯**: 点击后，在模态框中展示该场景评测的完整 `trace` JSON 对象。

#### `trace` 对象核心字段
| 字段名 | 类型 | 说明 | 来源文件 |
|---|---|---|---|
| `recall_scenarios` | `List[Dict]` | **召回层**: 记录所有被召回的场景及其相似度。 | `trace_five_cases.py` |
| `rerank_scenarios` | `List[Dict]` | **重排层**: 记录经过重排后的场景列表及分数。 | `trace_five_cases.py` |
| `final_prompt` | `str` | **Prompt层**: 记录最终构建并发送给大模型的完整 Prompt。 | `trace_five_cases.py` |
| `llm_parsed` | `Dict` | **生成层**: 记录大模型返回并被解析后的结构化推荐结果。 | `trace_five_cases.py` |
| `ragas_scores` | `Dict` | **评分层**: 包含四大核心指标的详细评分。 | `official_ragas_evaluation.py` |
| `ground_truth` | `str` | **基准**: 用于本次评测的 `ground_truth`。 | `trace_five_cases.py` |
| `ragas_error` | `str` | **异常记录**: 如果 RAGAS 评估失败，记录错误信息。 | `trace_five_cases.py` |

### 💾 数据保存与历史记录

#### 导出功能 (Export)
- **CSV/Excel (摘要)**: 包含原始数据、四项 RAGAS 得分和综合分。
- **JSON (完整快照)**: 导出一个包含评测样本和完整 `trace` 信息的 JSON 文件，用于问题复现和深度分析。

#### 历史记录 (History)
历史记录列表应提供每次评测的关键信息，并支持查看完整快照。

| 评测时间 | 数据源 | 模型 | Faithfulness | Answer Relevancy | Context Precision | Context Recall | **综合分** | 操作 |
|---|---|---|---|---|---|---|---|---|
| 2024-12-20 10:15 | `db` | gpt-4-turbo | 0.95 | 0.91 | 0.88 | 0.93 | **0.918** | [**查看快照**] [删除] |
| 2024-12-19 14:30 | `test.xlsx` | deepseek-r1 | 0.85 | 0.82 | 0.90 | 0.88 | **0.863** | [**查看快照**] [删除] |

- **查看快照**: 点击后，应在模态框或新页面中以 **格式化、可读性高** 的方式（如 JSON 高亮视图）展示该次评测的完整 `trace` JSON 对象内容。这使用户能清晰地看到从召回、重排到最终生成和评分的全过程。

---

## 🎨 页面设计

### 🏗️ 整体布局

页面采用自上而下的经典布局，分为四个核心区域，确保操作流程的直观性和逻辑性。

```
┌─────────────────────────────────────────────┐
│ 头部区域 (Header)                           │
│ <h1>RAGAS 评测</h1> <Tag>数据总量: 1,234条</Tag> │
├─────────────────────────────────────────────┤
│ 配置区域 (Configuration) [可折叠]             │
│ ┌─────────┐ ┌─────────┐ ┌─────────┐       │
│ │模型选择 │ │ API密钥 │ │ 端点URL │ [保存]  │
│ └─────────┘ └─────────┘ └─────────┘       │
├─────────────────────────────────────────────┤
│ 主内容区域 (Main Content)                   │
│ ┌─────────────────────────────────────┐   │
│ │ [数据源选择] [文件上传]             │   │
│ │                                     │   │
│ │ <数据预览表格 / 评测结果展示>       │   │
│ └─────────────────────────────────────┘   │
├─────────────────────────────────────────────┤
│ 操作与历史区域 (Actions & History)          │
│ [开始评测] [导出结果 ▼] [查看历史记录]     │
└─────────────────────────────────────────────┘
```

### 🧩 UI组件与交互

| 区域 | 核心组件 | 交互说明 |
|---|---|---|
| **头部区域** | `<h1>`, `Tag` | 固定显示页面标题和当前数据库中的数据总量。 |
| **配置区域** | `Collapse`, `Select`, `Input.Password`, `Button` | - 默认折叠，点击展开进行模型配置。<br>- “保存”按钮将配置存入 `localStorage`。 |
| **主内容区** | `Tabs`, `Upload`, `Table` | - **数据源选择**: 使用 `Tabs` 或 `Radio.Group` 切换“数据库”和“文件上传”。<br>- **数据预览**: 使用 `Table` 组件展示，支持分页和滚动。<br>- **结果展示**: 评测完成后，此区域更新为结果总结和详细数据表格。 |
| **操作区** | `Button`, `Dropdown` | - **开始评测**: 核心操作按钮，评测进行中应禁用。<br>- **导出结果**: 使用 `Dropdown` 提供多种导出格式（CSV, JSON）。 |

### 🎨 设计原则

| 原则 | 具体实施 | 示例 |
|---|---|---|
| **一致性 (Consistency)** | 全面采用 Ant Design 组件库，保持统一的视觉风格和交互模式。 | `Button`, `Table`, `Modal`, `Tag` |
| **状态清晰 (Clarity)** | 通过颜色编码、图标和加载指示器，明确传达系统当前状态。 | 🟢 成功 `Spin` 加载中 🔴 错误 |
| **响应式 (Responsiveness)** | 采用栅格系统布局，确保在不同分辨率的设备上（特别是主流桌面显示器）都能获得良好的视觉和操作体验。 | `Row`, `Col` |
| **反馈及时 (Feedback)** | 用户的每一个重要操作（如保存配置、开始评测、导出成功/失败）都应有明确的 `message` 或 `notification` 反馈。 | `message.success('配置已保存！')` |

---

## 🧪 测试规范
本部分定义了确保 RAGAS 评测页面质量所需的核心测试用例。

| 测试类别 | 测试场景 | 预期结果 | 优先级 |
|---|---|---|---|
| **数据源** | 从数据库加载数据 | 成功加载并显示数据列表，分页功能正常。 | **P0** |
| | 上传格式正确的 Excel/CSV 文件 | 文件成功解析，数据显示在预览表格中。 | **P0** |
| | 上传缺少 `question` 列的文件 | 提示“文件缺少必填列：question”，上传失败。 | **P1** |
| | 上传 `contexts` 格式错误的行 | 评测时后端返回该行的处理错误，前端能标记出错误行。 | **P1** |
| **模型配置** | 保存模型配置 | 点击保存后，刷新页面，配置依然存在。 | **P0** |
| | 使用错误的 API Key | 评测失败，并提示“API Key 认证失败”或相关错误。 | **P1** |
| **评测功能** | 执行一次完整的评测 | 评测成功，页面正确展示综合评分和各项指标。 | **P0** |
| | 查看评测结果的快照 | 模态框或新页面能正确、完整地展示 `trace` 对象的全部层级和内容。 | **P0** |
| | `trace` 数据完整性 | 历史记录中的 `trace` 数据与后端 `trace_five_cases.py` 生成的结构和字段一致。 | **P0** |
| | RAGAS 评分失败 | 如果 `ragas_scores` 为空，历史记录中对应评分应显示为 `N/A`，快照中应显示 `ragas_error` 信息。 | **P1** |
| **历史记录** | 删除一条历史记录 | 该记录从列表中移除。 | **P1** |
| | 导出 JSON 快照 | 成功下载包含完整评测信息的 JSON 文件。 | **P0** |

---

## 📎 附录

### 附录 A: API 端点参考

#### A.1 API 设计总览

为了支持 RAGAS 评测页面的功能，后端需要提供一套清晰、高效的 API。本文档定义了所有相关的端点、数据结构和交互流程。

- **基础路径**: `/api/v1/acrac/ragas`
- **认证**: (待定，根据项目统一认证方案)

---

#### A.2 数据上传与管理 (`/data`)

**端点**: `POST /api/v1/acrac/ragas/data`

**功能**: 接收并解析用户上传的 Excel/CSV 文件，将其内容存储到临时数据库或缓存中，并返回一个唯一的数据集 ID。

**请求体 (`multipart/form-data`)**:

| 字段名 | 类型   | 描述                                   | 是否必须 |
| ------ | ------ | -------------------------------------- | -------- |
| `file` | File   | 用户上传的评测数据集，格式为 Excel 或 CSV。 | 是       |

**成功响应 (`200 OK`)**:

```json
{
  "dataset_id": "ds_20231027103045_xyz",
  "message": "文件上传成功，已解析并加载了 15 条临床场景。",
  "columns": ["题号", "临床场景", "首选检查项目（标准化）"],
  "preview_data": [
    { "题号": 1, "临床场景": "场景A...", "首选检查项目（标准化）": "检查1..." },
    { "题号": 2, "临床场景": "场景B...", "首选检查项目（标准化）": "检查2..." }
  ]
}
```

**错误响应**:

- `400 Bad Request`: 文件格式不正确、缺少必要列等。
- `500 Internal Server Error`: 服务器内部处理错误。

---

#### A.3 启动批量评测 (`/evaluate`)

**端点**: `POST /api/v1/acrac/ragas/evaluate`

**功能**: 接收评测请求，异步启动一个“一对多”（一个问题 VS 多个临床场景）的 RAGAS 批量评测任务。

**请求体 (`application/json`)**:

```json
{
  "dataset_id": "ds_20231027103045_xyz",
  "question": "请根据患者的临床场景，推荐最合适的影像检查项目。",
  "selected_scenarios": [1, 2, 5, 8,-20]
  “groudtruth”:[]
}
```

| 字段名               | 类型      | 描述                                       | 是否必须 |
| -------------------- | --------- | ------------------------------------------ | -------- |
| `dataset_id`         | `string`  | 从 `/data` 接口获取的数据集 ID。             | 是       |
| `question`           | `string`  | 用户输入的评测问题。                       | 是       |
| `selected_scenarios` | `array`   | 用户勾选的临床场景 `题号` 列表。           | 是       |

**成功响应 (`202 Accepted`)**:

此端点为异步接口，立即返回一个任务 ID，用于后续查询评测状态和结果。

```json
{
  "task_id": "task_20231027103500_abc",
  "message": "评测任务已成功启动，正在后台处理中。"
}
```

**错误响应**:

- `400 Bad Request`: 请求参数校验失败（如 `dataset_id` 无效）。
- `404 Not Found`: `dataset_id` 或 `selected_scenarios` 中的场景不存在。

---

#### A.4 获取评测历史与结果 (`/history`)

**端点**:
1.  `GET /api/v1/acrac/ragas/history` (获取列表)
2.  `GET /api/v1/acrac/ragas/history/{task_id}` (获取单个任务详情)
3.  `DELETE /api/v1/acrac/ragas/history/{task_id}` (删除任务)

**功能**: 提供评测历史记录的查询、单个任务结果的追溯以及历史记录的删除功能。

##### A.4.1 获取历史记录列表

**成功响应 (`200 OK`)**:

返回一个评测任务的摘要列表，按创建时间降序排列。

```json
{
  "history": [
    {
      "task_id": "task_20231027103500_abc",
      "question": "患者，女，35岁，右下腹疼痛...",
      "status": "completed",
      "created_at": "2023-10-27T10:35:00Z",
      "average_score": 0.85
    },
    {
      "task_id": "task_20231026182000_def",
      "question": "患者，男，60岁，突发胸痛...",
      "status": "failed",
      "created_at": "2023-10-26T18:20:00Z",
      "average_score": null
    }
  ]
}
```

| 字段名          | 类型     | 描述                               |
| --------------- | -------- | ---------------------------------- |
| `status`        | `string` | `pending`, `running`, `completed`, `failed` |
| `average_score` | `float`  | 评测完成后的综合平均分。           |

##### A.4.2 获取单个任务详情

**成功响应 (`200 OK`)**:

返回指定 `task_id` 的完整评测结果。

```json
{
  "task_id": "task_20231027103500_abc",
  "question": "患者，女，35岁，右下腹疼痛...",
  "status": "completed",
  "created_at": "2023-10-27T10:35:00Z",
  "summary": {
    "average_ragas_score": 0.85,
    "average_faithfulness": 0.9,
    "average_answer_relevancy": 0.8,
    "average_context_precision": 0.95,
    "average_context_recall": 0.75
  },
  "details": [
    {
      "scenario_id": 1,
      "clinical_scenario": "场景A...",
      "ground_truth": "检查1...",
      "generated_answer": "生成的答案1...",
      "retrieved_context": "检索到的上下文1...",
      "scores": {
        "ragas_score": 0.88,
        "faithfulness": 0.92,
        "answer_relevancy": 0.85,
        "context_precision": 0.98,
        "context_recall": 0.77
      }
    }
  ]
}
```

**任务状态轮询**: 前端可以在调用 `/evaluate` 后，定期轮询此端点 (`GET /history/{task_id}`) 以获取最新任务状态。当 `status` 变为 `completed` 或 `failed` 时停止。

##### A.4.3 删除历史记录

**成功响应 (`204 No Content`)**:

表示服务器已成功删除该历史记录。

**错误响应**:

- `404 Not Found`: `task_id` 不存在。

---

### 附录 B: 后台服务架构与工作流设计

#### B.1 设计思想

为解决RAGAS批量评测中单个任务耗时长、资源占用大、用户等待体验差的问题，我们采用**异步任务队列**架构。该架构将重量级的计算任务（如RAG-LLM推理、RAGAS评估）与前端Web服务（API）彻底解耦。

核心优势：
- **非阻塞式API**：用户提交评测任务后可立即获得响应，无需等待任务完成。
- **高并发处理**：通过扩展任务处理单元（Worker），系统可以同时处理多个评测任务。
- **可伸缩性与健壮性**：任务队列提供了任务重试、错误处理和持久化机制，确保了系统的稳定性和可靠性。
- **实时进度跟踪**：前端可以通过轮询API获取任务的实时状态和进度。

#### B.2 系统组件

1.  **Web服务 (API Server)**:
    -   **技术栈**: FastAPI
    -   **职责**:
        -   提供RESTful API端点（`/data`, `/evaluate`, `/history`）。
        -   负责用户请求的验证、任务的初始化，并将计算密集型任务分发给任务队列。
        -   提供任务状态查询接口。

2.  **任务队列 (Task Queue)**:
    -   **技术栈**: Celery
    -   **职责**:
        -   管理需要异步执行的任务队列。
        -   作为生产者（API服务）和消费者（计算节点）之间的缓冲。

3.  **消息代理 (Message Broker)**:
    -   **技术栈**: Redis
    -   **职责**:
        -   存储Celery的任务消息，协调API服务和Celery Worker之间的通信。
        -   也可用于缓存任务状态和中间结果。

4.  **数据库 (Database)**:
    -   **技术栈**: PostgreSQL docker中已经安装了 
    -   **职责**:
        -   持久化存储评测任务信息（`EvaluationTask`），包括任务ID、状态、创建时间、结果摘要等。
        -   存储每个临床场景的详细评测结果（`ScenarioResult`），包括输入、中间数据、LLM输出和RAGAS分数。

5.  **文件存储 (File Storage)**:
    -   **技术栈**: 本地文件系统或云存储 (如 S3)
    -   **职责**: 存储用户上传的原始评测数据集（如`.xlsx`文件）。

#### B.3 核心工作流

##### B.3.1 数据上传与任务初始化 (`/data`)

1.  **接收文件**: 用户通过 `POST /api/v1/acrac/ragas/data` 上传包含评测数据的Excel文件（如 `影像测试样例-0318-1.xlsx`）。
2.  **解析与存储**:
    -   API服务接收文件并进行基础验证（如文件类型、大小）。
    -   服务解析Excel文件，读取**`题号`**、**`临床场景`**和**`首选检查项目（标准化）`**等关键列。
    -   在数据库中创建一个新的 `EvaluationTask` 记录，状态为 `PENDING`，并生成一个唯一的 `task_id`。
    -   将解析出的每一行数据（`临床场景`与`首选检查项目`）作为独立的 `ScenarioResult` 记录存入数据库，并与 `task_id` 关联。
3.  **返回响应**: API向用户返回 `task_id`，用于后续启动评测和查询状态。

##### B.3.2 启动批量评测 (`/evaluate`)

1.  **接收请求**: 用户通过 `POST /api/v1/acrac/ragas/evaluate` 并提供 `task_id` 来启动评测。
2.  **任务分发**:
    -   API服务验证 `task_id` 的有效性，并将任务状态更新为 `IN_PROGRESS`。
    -   API服务将 `task_id` 作为参数，向Celery任务队列推送一个主评测任务 (`start_evaluation_task`)。
3.  **异步执行**: Celery Worker接收任务后，开始执行以下步骤：
    -   **RAG-LLM推理**: Worker根据 `task_id` 从数据库中检索所有关联的 `临床场景`。对于每个场景，它会调用内部的RAG-LLM推理服务（类似于 `trace_five_cases.py` 中调用的API），获取推荐结果和中间数据（如召回列表、重排列表等）。
    -   **数据解析**: Worker解析推理服务返回的JSON结果，提取关键信息。
    -   **调用RAGAS评估**: Worker将推理结果和从数据库中读取的`首选检查项目（标准化）`（Ground Truth）格式化，提交给RAGAS评估模块，获取`faithfulness`, `answer_relevancy`等指标分数。
    -   **进度更新与结果存储**: 每处理完一个`临床场景`，Worker就更新该`ScenarioResult`记录的状态和结果，并更新主`EvaluationTask`的总体进度（例如，已完成 `x/total` 个）。
4.  **任务完成**: 所有场景处理完毕后，Worker计算最终的统计结果（如平均分、成功率等），更新 `EvaluationTask` 的状态为 `COMPLETED`，并存入最终结果。

##### B.3.3 获取评测历史与结果 (`/history`)

-   **`GET /history`**: 返回所有评测任务的列表（`task_id`, 状态, 创建时间, 结果摘要）。
-   **`GET /history/{task_id}`**: 返回特定任务的详细信息，包括总体统计结果和所有`ScenarioResult`的详细数据。
-   **`DELETE /history/{task_id}`**: 删除一个评测任务及其所有相关数据。

#### B.4 灵活性设计：数据适配器层 (Data Adapter)

为了应对未来RAG-LLM推理服务返回的中间数据格式可能发生变化的情况，我们引入**数据适配器层**以确保系统的灵活性和可维护性。

-   **核心思想**: 将“数据解析”步骤具体化为一个独立的、可配置的适配器。该适配器作为RAG-LLM推理输出和RAGAS评估输入之间的桥梁。
-   **工作流程**:
    1.  **定义内部标准模型**: 系统内部定义一个严格、稳定的数据结构，作为RAGAS评估模块的唯一输入格式（例如，包含 `question`, `answer`, `contexts`, `ground_truth` 字段的Python `dataclass`）。
    2.  **实现转换逻辑**: 数据适配器接收RAG-LLM推理服务返回的原始JSON，并负责将其解析、提取和转换为内部标准模型。
    3.  **隔离变化**: 如果未来RAG-LLM的输出格式发生变更（例如，字段重命名、结构调整），我们只需要修改数据适配器中的映射逻辑，而RAGAS评估部分的核心代码无需任何改动。

这种设计将数据源的变化隔离开来，极大地提高了系统的健壮性和可扩展性。

---

### 附录 C: API端点参考 (V2 - 异步架构)

### 附录 D: 数据结构（JSON 示例）

#### Excel/CSV 数据映射示例
以下示例展示了上传的 `.xlsx` 文件内容如何映射为评测系统所需的数据结构。

| 文件列名 | 示例内容 | 映射到系统字段 | 说明 |
|---|---|---|---|
| `题号` | `CASE-001` | `id` | 唯一标识符 |
| `临床场景` | `"患者，男，68岁，因“反复上腹部隐痛不适3月余”入院..."` | `context` | RAG 系统召回和推理的依据 |
| `首选检查项目（标准化）` | `"上腹部CT增强扫描"` | `ground_truth` | 用于评估答案准确性的基准 |

#### `contexts` 字段格式
```json
"[\"上下文示例一\", \"上下文示例二\"]"
```

#### `trace` 对象结构 (简化)
```json
{
  "recall_scenarios": [
    {"id": "scene_A", "score": 0.9},
    {"id": "scene_B", "score": 0.85}
  ],
  "rerank_scenarios": [
    {"id": "scene_A", "score": 0.95}
  ],
  "final_prompt": "...",
  "llm_parsed": {
    "recommendation": "..."
  },
  "ragas_scores": {
    "faithfulness": 0.95,
    "answer_relevancy": 0.91,
    "context_precision": 0.88,
    "context_recall": 0.93
  },
  "ground_truth": "...",
  "ragas_error": null
}
```

### 术语表
| 术语 | 解释 |
|---|---|
| **Faithfulness** | 生成的答案是否忠实于上下文。 |
| **Answer Relevancy** | 答案与问题的相关性。 |
| **Context Precision** | 召回的上下文与答案生成的相关性。 |
| **Context Recall** | 召回的上下文是否完全覆盖了 `ground_truth` 的信息。 |