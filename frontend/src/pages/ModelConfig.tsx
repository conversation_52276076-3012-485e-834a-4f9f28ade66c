import React, { useEffect, useState } from 'react'
import { Card, Form, Input, Button, Space, message, Alert, Row, Col, Switch, Typography, Divider } from 'antd'
import { api } from '../api/http'

const { Text } = Typography

const ModelConfig: React.FC = () => {
  const [form] = Form.useForm()
  const [loading, setLoading] = useState(false)
  const [reloading, setReloading] = useState(false)
  const [config, setConfig] = useState<any>(null)

  const load = async () => {
    try {
      const r = await api.get('/api/v1/admin/data/models/config')
      setConfig(r.data)
      form.setFieldsValue({
        embedding_model: r.data.embedding_model,
        llm_model: r.data.llm_model,
        reranker_model: r.data.reranker_model,
        base_url: r.data.base_url,
        siliconflow_api_key: '',
        openai_api_key: '',
      })
    } catch (e: any) {
      message.error('加载失败：' + (e?.response?.data?.detail || e.message))
    }
  }

  useEffect(() => { load() }, [])

  const save = async (v: any) => {
    setLoading(true)
    try {
      const r = await api.post('/api/v1/admin/data/models/config', v)
      message.success('已保存（部分配置需重启生效）')
      if (r.data.requires_restart) {
        message.warning('建议重启后端服务以完全生效')
      }
      load() // 重新加载配置
    } catch (e: any) {
      message.error('保存失败：' + (e?.response?.data?.detail || e.message))
    } finally {
      setLoading(false)
    }
  }

  const reloadSvc = async () => {
    setReloading(true)
    try {
      await api.post('/api/v1/admin/data/models/reload')
      message.success('已重载服务实例')
    } catch (e: any) {
      message.error('重载失败：' + (e?.response?.data?.detail || e.message))
    } finally {
      setReloading(false)
    }
  }

  return (
    <div>
      <div className='page-title'>模型配置</div>
      
      <Card title="当前配置状态" style={{ marginBottom: 16 }}>
        {config && (
          <Row gutter={16}>
            <Col span={12}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div><Text strong>API Keys 状态：</Text></div>
                <div>
                  <Text type={config.keys?.siliconflow_api_key ? 'success' : 'danger'}>
                    SiliconFlow API Key: {config.keys?.siliconflow_api_key ? '已配置' : '未配置'}
                  </Text>
                </div>
                <div>
                  <Text type={config.keys?.openai_api_key ? 'success' : 'warning'}>
                    OpenAI API Key: {config.keys?.openai_api_key ? '已配置' : '未配置'}
                  </Text>
                </div>
              </Space>
            </Col>
            <Col span={12}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <div><Text strong>当前模型：</Text></div>
                <div>Embedding: {config.embedding_model || '未设置'}</div>
                <div>LLM: {config.llm_model || '未设置'}</div>
                <div>Reranker: {config.reranker_model || '未设置'}</div>
                <div>Base URL: {config.base_url || '未设置'}</div>
              </Space>
            </Col>
          </Row>
        )}
      </Card>

      <Card title="模型配置">
        <Form form={form} layout='vertical' onFinish={save} initialValues={{ 
          embedding_model: '', 
          llm_model: '', 
          reranker_model: '', 
          base_url: '',
          siliconflow_api_key: '',
          openai_api_key: ''
        }}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name='embedding_model' label='Embedding 模型'>
                <Input placeholder='BAAI/bge-m3' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name='reranker_model' label='重排模型'>
                <Input placeholder='BAAI/bge-reranker-v2-m3' />
              </Form.Item>
            </Col>
          </Row>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name='llm_model' label='LLM 模型'>
                <Input placeholder='Qwen/Qwen2.5-32B-Instruct' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name='base_url' label='LLM Base URL'>
                <Input placeholder='https://api.siliconflow.cn/v1' />
              </Form.Item>
            </Col>
          </Row>

          <Divider orientation="left">API Key 配置</Divider>
          
          <Alert 
            message="安全提示" 
            description="API Key 是敏感信息，请妥善保管。输入的 API Key 将被安全存储在服务器端，不会在前端明文显示。" 
            type="warning" 
            showIcon 
            style={{ marginBottom: 16 }}
          />

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item 
                name='siliconflow_api_key' 
                label='SiliconFlow API Key'
                extra="用于访问 SiliconFlow 模型服务"
              >
                <Input.Password placeholder='输入 SiliconFlow API Key' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item 
                name='openai_api_key' 
                label='OpenAI API Key（可选）'
                extra="用于访问 OpenAI 模型服务"
              >
                <Input.Password placeholder='输入 OpenAI API Key（可选）' />
              </Form.Item>
            </Col>
          </Row>

          <Alert 
            message="配置说明" 
            description="修改配置后建议重启后端服务以确保所有配置完全生效。重载服务按钮可以应用部分配置（如Base URL、模型名等）而无需重启。API Key 配置会立即生效。" 
            type="info" 
            showIcon 
            style={{ marginBottom: 16 }}
          />

          <Space>
            <Button type='primary' htmlType='submit' loading={loading}>保存配置</Button>
            <Button onClick={reloadSvc} loading={reloading}>重载服务</Button>
            <Button onClick={load}>刷新状态</Button>
          </Space>
        </Form>
      </Card>
    </div>
  )
}

export default ModelConfig
