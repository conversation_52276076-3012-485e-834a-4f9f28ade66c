# GEMINI.md

## Project Overview

This project is a full-stack medical imaging recommendation system called ACRAC (American College of Radiology Appropriateness Criteria). It provides intelligent recommendations for medical imaging procedures based on patient symptoms and clinical history.

The frontend is a React application built with Vite and TypeScript, using Ant Design for UI components. The backend is a Python application using the FastAPI framework, with SQLAlchemy as the ORM. It leverages a PostgreSQL database with the `pgvector` extension for efficient similarity searches on medical-legal data. The system uses the SiliconFlow BGE-M3 model for generating vector embeddings.

The entire application is containerized using Docker and orchestrated with Docker Compose, which defines services for the frontend, backend, database, and a Redis cache.

## Building and Running

### Prerequisites

*   Docker
*   Docker Compose
*   Node.js
*   Python

### Development

1.  **Start services:**
    ```bash
    docker-compose up -d
    ```

2.  **Run the backend:**
    ```bash
    cd backend
    python -m venv venv
    source venv/bin/activate
    pip install -r requirements.txt
    uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
    ```

3.  **Run the frontend:**
    ```bash
    cd frontend
    npm install
    npm run dev
    ```

### Production

To build and run the application for production:

```bash
docker-compose -f docker-compose.prod.yml up --build -d
```

## Development Conventions

*   **Backend:** The backend follows a standard FastAPI project structure, with code organized into `api`, `core`, `models`, `schemas`, and `services` directories. It uses SQLAlchemy for database interactions and Alembic for database migrations.
*   **Frontend:** The frontend is a standard React project with TypeScript. It uses `vite` for development and bundling.
*   **Dependencies:** Backend dependencies are managed with `pip` and `requirements.txt`. Frontend dependencies are managed with `npm` and `package.json`.
*   **Testing:** The backend includes tests that can be run with `pytest`. The `README.md` provides instructions on how to run specific test scripts.
