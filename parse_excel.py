import pandas as pd
import json
import sys

def parse_excel_to_ragas_format(excel_path, num_rows=5):
    """
    解析Excel文件并转换为RAGAS评测格式
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_path)
        
        print(f"Excel文件总行数: {len(df)}")
        print(f"列名: {list(df.columns)}")
        print("\n前几行数据:")
        print(df.head())
        
        # 提取前num_rows行数据
        sample_data = df.head(num_rows)
        
        # 转换为RAGAS格式
        ragas_data = []
        
        for index, row in sample_data.iterrows():
            # 根据用户要求的正确格式进行映射
            # clinical_query: 固定问题
            # contexts: Excel中的"临床场景"内容
            # ground_truth: Excel中的"首选检查项目（标准化）"内容
            
            # 获取列数据，处理可能的列名变化
            clinical_scenario = ""
            standard_procedure = ""
            
            # 尝试通过列名获取数据
            if '临床场景' in df.columns:
                clinical_scenario = str(row['临床场景']) if pd.notna(row['临床场景']) else ""
            elif len(row) > 1:
                clinical_scenario = str(row.iloc[1]) if pd.notna(row.iloc[1]) else ""
                
            if '首选检查项目（标准化）' in df.columns:
                standard_procedure = str(row['首选检查项目（标准化）']) if pd.notna(row['首选检查项目（标准化）']) else ""
            elif len(row) > 2:
                standard_procedure = str(row.iloc[2]) if pd.notna(row.iloc[2]) else ""
            
            item = {
                "clinical_query": "根据用户数据推荐检查项目",  # 固定的问题
                "contexts": [clinical_scenario],  # 临床场景作为上下文
                "ground_truth": standard_procedure  # 首选检查项目作为标准答案
            }
            ragas_data.append(item)
        
        return ragas_data
        
    except Exception as e:
        print(f"解析Excel文件时出错: {str(e)}")
        return None

if __name__ == "__main__":
    excel_path = "/Users/<USER>/git_project_vscode/09_medical/ACRAC-web/影像测试样例-0318-1.xlsx"
    
    # 解析Excel文件
    ragas_data = parse_excel_to_ragas_format(excel_path, 5)
    
    if ragas_data:
        # 保存为JSON文件
        output_path = "/Users/<USER>/git_project_vscode/09_medical/ACRAC-web/real_test_data.json"
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(ragas_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n成功转换 {len(ragas_data)} 条数据")
        print(f"数据已保存到: {output_path}")
        
        # 打印转换后的数据
        print("\n转换后的RAGAS格式数据:")
        print(json.dumps(ragas_data, ensure_ascii=False, indent=2))
    else:
        print("解析失败")
        sys.exit(1)