# ACRAC性能优化指南

## 1. 性能优化原则

### 1.1 用户体验优先
性能优化的首要目标是提升用户体验，重点关注用户可感知的性能指标：
- 首屏加载时间
- 交互响应速度
- 页面流畅度
- 内存使用效率

### 1.2 数据驱动优化
基于实际性能数据进行优化决策，避免主观臆断：
- 使用性能监控工具收集数据
- 建立性能基准线
- 持续监控性能指标
- 量化优化效果

### 1.3 渐进式优化
采用渐进式优化策略，从关键路径优化开始：
- 优先优化核心功能
- 逐步优化次要功能
- 保持优化的可持续性
- 避免过度优化

## 2. 关键性能指标(KPIs)

### 2.1 核心Web指标(Core Web Vitals)
1. **Largest Contentful Paint (LCP)**: < 2.5秒
   - 衡量页面主要内容的加载速度

2. **First Input Delay (FID)**: < 100毫秒
   - 衡量页面交互响应速度

3. **Cumulative Layout Shift (CLS)**: < 0.1
   - 衡量页面视觉稳定性

### 2.2 其他重要指标
1. **First Contentful Paint (FCP)**: < 1.8秒
2. **Time to Interactive (TTI)**: < 3.8秒
3. **Total Blocking Time (TBT)**: < 200毫秒
4. **Bundle Size**: < 500KB (压缩后)

## 3. 构建优化

### 3.1 代码分割
合理分割代码包，实现按需加载：

```javascript
// 路由级代码分割
const Home = () => import('@/views/Home.vue')
const Browse = () => import('@/views/Browse.vue')
const Search = () => import('@/views/Search.vue')

// 组件级代码分割
const AsyncComponent = defineAsyncComponent(() => 
  import('@/components/HeavyComponent.vue')
)

// 条件加载
if (process.env.NODE_ENV === 'development') {
  import('@/utils/debugTools').then(module => {
    // 开发工具只在开发环境加载
  })
}
```

### 3.2 Tree Shaking
移除未使用的代码，减小包体积：

```javascript
// 正确：按需导入
import { debounce, throttle } from 'lodash-es'

// 错误：全量导入
import _ from 'lodash'

// 正确：只导入需要的组件
import { ElButton, ElInput } from 'element-plus'

// 错误：导入整个库
import ElementPlus from 'element-plus'
```

### 3.3 压缩和混淆
配置构建工具进行代码压缩：

```javascript
// vite.config.js
export default defineConfig({
  build: {
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          // 第三方库分包
          vendor: ['vue', 'vue-router', 'pinia'],
          ui: ['element-plus'],
          utils: ['lodash-es', 'axios']
        }
      }
    }
  }
})
```

## 4. 资源优化

### 4.1 图片优化
优化图片资源，减小加载时间：

```vue
<template>
  <picture>
    <!-- WebP格式优先 -->
    <source 
      type="image/webp" 
      :srcset="`${imageSrc}.webp`"
    >
    <!-- 备用格式 -->
    <img 
      :src="`${imageSrc}.jpg`"
      :alt="imageAlt"
      loading="lazy"
      decoding="async"
    >
  </picture>
</template>

<script>
export default {
  // 使用响应式图片
  computed: {
    imageSrc() {
      const screenWidth = window.innerWidth
      if (screenWidth < 768) {
        return '/images/small'
      } else if (screenWidth < 1200) {
        return '/images/medium'
      } else {
        return '/images/large'
      }
    }
  }
}
</script>
```

### 4.2 字体优化
优化字体加载，避免阻塞渲染：

```css
/* 字体预加载 */
@font-face {
  font-family: 'CustomFont';
  src: url('/fonts/custom.woff2') format('woff2');
  font-display: swap; /* 关键优化 */
}

/* 字体子集化 */
@font-face {
  font-family: 'CustomFont';
  src: url('/fonts/custom-latin.woff2') format('woff2');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+2000-206F, U+2074, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
  font-display: swap;
}
```

### 4.3 CSS优化
优化CSS加载和执行：

```scss
// 关键CSS内联
// 非关键CSS异步加载
const loadCSS = (href) => {
  const link = document.createElement('link')
  link.rel = 'stylesheet'
  link.href = href
  document.head.appendChild(link)
}

// 媒体查询优化
@media (max-width: 768px) {
  .desktop-only {
    display: none; // 避免加载不必要的样式
  }
}

// CSS变量减少重复
:root {
  --primary-color: #2E86AB;
  --border-radius: 8px;
}
```

## 5. 网络优化

### 5.1 缓存策略
合理配置HTTP缓存：

```javascript
// Vite配置
export default defineConfig({
  build: {
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        // 文件名哈希，支持长期缓存
        entryFileNames: 'js/[name].[hash].js',
        chunkFileNames: 'js/[name].[hash].js',
        assetFileNames: 'assets/[name].[hash].[ext]'
      }
    }
  }
})

// Service Worker缓存策略
self.addEventListener('fetch', event => {
  if (event.request.destination === 'image') {
    event.respondWith(
      caches.open('images').then(cache => {
        return cache.match(event.request).then(response => {
          return response || fetch(event.request).then(fetchResponse => {
            cache.put(event.request, fetchResponse.clone())
            return fetchResponse
          })
        })
      })
    )
  }
})
```

### 5.2 CDN优化
使用CDN加速资源加载：

```javascript
// 静态资源配置CDN
const CDN_BASE = process.env.NODE_ENV === 'production' 
  ? 'https://cdn.example.com' 
  : ''

export default {
  build: {
    rollupOptions: {
      output: {
        // CDN路径配置
        entryFileNames: `${CDN_BASE}/js/[name].[hash].js`,
        chunkFileNames: `${CDN_BASE}/js/[name].[hash].js`,
        assetFileNames: `${CDN_BASE}/assets/[name].[hash].[ext]`
      }
    }
  }
}
```

### 5.3 预加载和预获取
合理使用预加载和预获取：

```html
<!-- 关键资源预加载 -->
<link rel="preload" href="/fonts/custom.woff2" as="font" type="font/woff2" crossorigin>
<link rel="preload" href="/api/config" as="fetch" crossorigin>

<!-- 路由预获取 -->
<link rel="prefetch" href="/js/browse.[hash].js">
<link rel="prefetch" href="/js/search.[hash].js">
```

## 6. 运行时优化

### 6.1 虚拟滚动
处理大量数据列表时使用虚拟滚动：

```vue
<template>
  <div class="virtual-list" ref="container" @scroll="handleScroll">
    <div :style="{ height: totalHeight + 'px' }" class="virtual-container">
      <div 
        v-for="item in visibleItems" 
        :key="item.id"
        :style="{ transform: `translateY(${item.top}px)` }"
        class="virtual-item"
      >
        {{ item.data }}
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      items: [], // 大量数据
      containerHeight: 400,
      itemHeight: 50,
      visibleCount: 10,
      startIndex: 0
    }
  },
  
  computed: {
    totalHeight() {
      return this.items.length * this.itemHeight
    },
    
    visibleItems() {
      return this.items
        .slice(this.startIndex, this.startIndex + this.visibleCount)
        .map((item, index) => ({
          ...item,
          top: (this.startIndex + index) * this.itemHeight
        }))
    }
  },
  
  methods: {
    handleScroll(event) {
      const scrollTop = event.target.scrollTop
      this.startIndex = Math.floor(scrollTop / this.itemHeight)
    }
  }
}
</script>
```

### 6.2 防抖和节流
优化高频事件处理：

```javascript
import { debounce, throttle } from 'lodash-es'

export default {
  mounted() {
    // 防抖：搜索输入
    this.debouncedSearch = debounce(this.performSearch, 300)
    
    // 节流：窗口大小调整
    this.throttledResize = throttle(this.handleResize, 100)
    
    window.addEventListener('resize', this.throttledResize)
  },
  
  methods: {
    handleInput(value) {
      this.debouncedSearch(value)
    },
    
    performSearch(query) {
      // 实际搜索逻辑
    },
    
    handleResize() {
      // 窗口大小调整逻辑
    }
  },
  
  beforeUnmount() {
    window.removeEventListener('resize', this.throttledResize)
  }
}
```

### 6.3 计算属性缓存
合理使用计算属性缓存：

```javascript
export default {
  computed: {
    // 正确：纯函数计算，有缓存
    filteredItems() {
      return this.items.filter(item => item.active && item.visible)
    },
    
    // 正确：依赖响应式数据
    totalPrice() {
      return this.items.reduce((sum, item) => sum + item.price, 0)
    },
    
    // 错误：包含副作用，不应使用计算属性
    badExample() {
      console.log('This will run on every access')
      return this.items.filter(item => item.active)
    }
  }
}
```

## 7. 内存优化

### 7.1 组件销毁
及时清理组件资源：

```vue
<script>
export default {
  data() {
    return {
      timer: null,
      observer: null
    }
  },
  
  mounted() {
    // 定时器
    this.timer = setInterval(() => {
      // 定时任务
    }, 1000)
    
    // 观察器
    this.observer = new IntersectionObserver(this.handleIntersection)
    this.observer.observe(this.$refs.target)
  },
  
  beforeUnmount() {
    // 清理定时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
    
    // 清理观察器
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
    
    // 清理事件监听器
    window.removeEventListener('resize', this.handleResize)
  }
}
</script>
```

### 7.2 大数据处理
分批处理大数据：

```javascript
export default {
  methods: {
    async processLargeData(data) {
      const batchSize = 100
      const results = []
      
      for (let i = 0; i < data.length; i += batchSize) {
        const batch = data.slice(i, i + batchSize)
        const batchResults = await this.processBatch(batch)
        results.push(...batchResults)
        
        // 让出控制权，避免阻塞UI
        await this.$nextTick()
      }
      
      return results
    },
    
    async processBatch(batch) {
      // 批量处理逻辑
      return batch.map(item => this.processItem(item))
    }
  }
}
```

## 8. 监控和分析

### 8.1 性能监控
集成性能监控工具：

```javascript
// 性能监控初始化
class PerformanceMonitor {
  constructor() {
    this.init()
  }
  
  init() {
    // 监控Core Web Vitals
    if ('PerformanceObserver' in window) {
      this.observeLCP()
      this.observeFID()
      this.observeCLS()
    }
    
    // 监控资源加载
    this.observeResources()
  }
  
  observeLCP() {
    new PerformanceObserver((entryList) => {
      const entries = entryList.getEntries()
      const lastEntry = entries[entries.length - 1]
      this.reportMetric('LCP', lastEntry.startTime)
    }).observe({ entryTypes: ['largest-contentful-paint'] })
  }
  
  observeFID() {
    new PerformanceObserver((entryList) => {
      const firstInput = entryList.getEntries()[0]
      if (firstInput) {
        this.reportMetric('FID', firstInput.processingStart - firstInput.startTime)
      }
    }).observe({ entryTypes: ['first-input'] })
  }
  
  observeCLS() {
    let clsValue = 0
    new PerformanceObserver((entryList) => {
      for (const entry of entryList.getEntries()) {
        if (!entry.hadRecentInput) {
          clsValue += entry.value
        }
      }
      this.reportMetric('CLS', clsValue)
    }).observe({ entryTypes: ['layout-shift'] })
  }
  
  reportMetric(name, value) {
    // 发送性能数据到监控服务
    console.log(`Performance Metric ${name}: ${value}`)
  }
}

// 应用启动时初始化
new PerformanceMonitor()
```

### 8.2 错误监控
监控运行时错误：

```javascript
// 全局错误处理
window.addEventListener('error', (event) => {
  // 记录错误信息
  console.error('Global Error:', event.error)
  
  // 发送错误报告
  sendErrorReport({
    message: event.error.message,
    stack: event.error.stack,
    filename: event.filename,
    lineno: event.lineno,
    colno: event.colno
  })
})

// Promise错误处理
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled Promise Rejection:', event.reason)
  
  sendErrorReport({
    message: event.reason.message || event.reason,
    stack: event.reason.stack
  })
})

function sendErrorReport(errorInfo) {
  // 发送错误报告到监控服务
  fetch('/api/error-report', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      ...errorInfo,
      url: window.location.href,
      userAgent: navigator.userAgent,
      timestamp: Date.now()
    })
  })
}
```

## 9. 测试和评估

### 9.1 性能测试工具
使用专业工具进行性能测试：

```bash
# Lighthouse测试
npx lighthouse https://your-app.com --output json --output html

# WebPageTest测试
# 访问 https://www.webpagetest.org/

# PageSpeed Insights
# 访问 https://pagespeed.web.dev/
```

### 9.2 自动化测试
集成性能测试到CI/CD流程：

```javascript
// Jest性能测试
describe('Performance Tests', () => {
  test('Bundle size should be under 500KB', async () => {
    const stats = await getBundleStats()
    expect(stats.size).toBeLessThan(500 * 1024) // 500KB
  })
  
  test('LCP should be under 2.5 seconds', async () => {
    const lcp = await getLCPMetric()
    expect(lcp).toBeLessThan(2500) // 2.5秒
  })
  
  test('Memory usage should be reasonable', async () => {
    const memoryUsage = await getMemoryUsage()
    expect(memoryUsage).toBeLessThan(100 * 1024 * 1024) // 100MB
  })
})
```

## 10. 持续优化

### 10.1 性能预算
建立性能预算约束：

```json
{
  "performance-budget": {
    "first-contentful-paint": 1800,
    "largest-contentful-paint": 2500,
    "first-input-delay": 100,
    "cumulative-layout-shift": 0.1,
    "javascript-execution-time": 2000,
    "bundle-size": 512000,
    "image-size": 1024000
  }
}
```

### 10.2 定期审计
定期进行性能审计：

```markdown
## 月度性能审计清单

### 构建优化
- [ ] 检查包体积变化
- [ ] 审查Tree Shaking效果
- [ ] 优化代码分割策略

### 资源优化
- [ ] 压缩图片资源
- [ ] 优化字体加载
- [ ] 清理未使用资源

### 运行时优化
- [ ] 检查内存泄漏
- [ ] 优化组件销毁
- [ ] 审查事件监听器

### 监控数据
- [ ] 分析Core Web Vitals
- [ ] 检查用户投诉
- [ ] 评估优化效果
```

通过遵循这些性能优化指南，ACRAC系统将能够提供快速、流畅的用户体验，同时保持良好的可维护性。