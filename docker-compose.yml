services:
  # PostgreSQL with pgvector
  postgres:
    image: pgvector/pgvector:pg15
    container_name: acrac_postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: acrac_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./deployment/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - acrac_network

  # Redis for caching and Celery
  redis:
    image: redis:7-alpine
    container_name: acrac_redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - acrac_network

  # Backend API
  backend:
    build: 
      context: ./backend
      dockerfile: Dockerfile
    container_name: acrac_backend
    volumes:
      - ./backend:/app
      - backend_logs:/app/logs
    ports:
      - "8001:8000"
    environment:
      - DATABASE_URL=********************************************/acrac_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/acrac/health"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - acrac_network

  celery_worker:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: acrac_celery_worker
    command: celery -A app.celery_app worker --loglevel=info --queues=ragas_queue
    volumes:
      - ./backend:/app
    environment:
      - DATABASE_URL=********************************************/acrac_db
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/1
      - CELERY_RESULT_BACKEND=redis://redis:6379/2
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD-SHELL", "celery -A app.celery_app inspect ping -d celery@$$(hostname) || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - acrac_network

#  # Frontend (development)
#  frontend:
#    build:
#      context: ./frontend
#      dockerfile: Dockerfile.dev
#    container_name: acrac_frontend
#    volumes:
#      - ./frontend:/app
#      - /app/node_modules
#    ports:
#      - "8001:8001"
#    environment:
#        - VITE_API_BASE_URL=http://localhost:8001
#    depends_on:
#      - backend
#    networks:
#      - acrac_network

  # Nginx reverse proxy
  nginx:
    image: nginx:alpine
    container_name: acrac_nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./deployment/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./deployment/nginx/conf.d:/etc/nginx/conf.d
    depends_on:
      - backend
    networks:
      - acrac_network

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local

networks:
  acrac_network:
    driver: bridge
